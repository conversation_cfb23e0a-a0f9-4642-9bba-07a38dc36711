#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger le problème de flou de l'icône dans la barre des tâches
"""

import os
import sys
import subprocess
from PIL import Image

def check_icon_quality():
    """Vérifier la qualité de l'icône actuelle"""
    
    print("🔍 DIAGNOSTIC DU PROBLÈME DE FLOU")
    print("=" * 50)
    
    icon_path = "qr_icone.ico"
    
    if not os.path.exists(icon_path):
        print(f"❌ Icône non trouvée: {icon_path}")
        return False
    
    try:
        # Ouvrir l'icône avec PIL
        with Image.open(icon_path) as img:
            print(f"✅ Icône trouvée: {icon_path}")
            print(f"📊 Format: {img.format}")
            print(f"📊 Mode: {img.mode}")
            print(f"📊 Taille principale: {img.size[0]}x{img.size[1]} px")
            
            # Vérifier les tailles critiques pour la barre des tâches
            critical_sizes = [16, 32, 48]
            
            print("\n🎯 Vérification des tailles critiques pour la barre des tâches:")
            print("-" * 50)
            
            # Cette méthode ne peut pas vérifier toutes les tailles dans un ICO
            # mais on peut au moins confirmer que l'icône s'ouvre
            print("✅ L'icône s'ouvre correctement avec PIL")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de l'ouverture: {e}")
        return False

def create_high_quality_icon():
    """Créer une icône haute qualité optimisée pour éviter le flou"""
    
    print("\n🎨 CRÉATION D'UNE ICÔNE OPTIMISÉE")
    print("=" * 50)
    
    try:
        # Ouvrir l'icône existante
        with Image.open("qr_icone.ico") as img:
            # Convertir en RGBA si nécessaire
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # Créer les tailles optimisées pour éviter le flou
            sizes = [16, 24, 32, 48, 64, 96, 128, 256]
            images = []
            
            print("🔧 Génération des tailles optimisées:")
            
            for size in sizes:
                # Redimensionner avec un algorithme de haute qualité
                resized = img.resize((size, size), Image.Resampling.LANCZOS)
                
                # Appliquer un léger sharpening pour les petites tailles
                if size <= 32:
                    from PIL import ImageFilter
                    resized = resized.filter(ImageFilter.SHARPEN)
                
                images.append(resized)
                print(f"  ✅ {size}x{size} px - Optimisé")
            
            # Sauvegarder la nouvelle icône
            backup_path = "qr_icone_backup.ico"
            new_path = "qr_icone_optimized.ico"
            
            # Faire une sauvegarde
            if os.path.exists("qr_icone.ico"):
                import shutil
                shutil.copy2("qr_icone.ico", backup_path)
                print(f"💾 Sauvegarde créée: {backup_path}")
            
            # Sauvegarder la nouvelle icône
            images[0].save(
                new_path,
                format='ICO',
                sizes=[(img.width, img.height) for img in images],
                append_images=images[1:]
            )
            
            print(f"✅ Icône optimisée créée: {new_path}")
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")
        return False

def apply_windows_dpi_fix():
    """Appliquer des corrections spécifiques à Windows pour le DPI"""
    
    print("\n🖥️  APPLICATION DES CORRECTIONS WINDOWS DPI")
    print("=" * 50)
    
    # Créer un manifeste pour la compatibilité DPI
    manifest_content = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <application xmlns="urn:schemas-microsoft-com:asm.v3">
    <windowsSettings>
      <dpiAware xmlns="http://schemas.microsoft.com/SMI/2005/WindowsSettings">true</dpiAware>
      <dpiAwareness xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">PerMonitorV2</dpiAwareness>
    </windowsSettings>
  </application>
</assembly>'''
    
    try:
        with open('app.manifest', 'w', encoding='utf-8') as f:
            f.write(manifest_content)
        
        print("✅ Manifeste DPI créé: app.manifest")
        print("💡 Ce fichier sera utilisé lors de la compilation PyInstaller")
        return True
        
    except Exception as e:
        print(f"❌ Erreur création manifeste: {e}")
        return False

def update_build_script():
    """Mettre à jour le script de build pour inclure les corrections"""
    
    print("\n🔧 MISE À JOUR DU SCRIPT DE BUILD")
    print("=" * 50)
    
    try:
        # Lire le script de build actuel
        with open('build_native.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier si les corrections sont déjà présentes
        if '--manifest=' in content:
            print("✅ Script de build déjà optimisé")
            return True
        
        # Ajouter les arguments pour le manifeste et l'icône optimisée
        if "'--icon=' + icon_path," in content:
            # Remplacer la ligne d'icône
            content = content.replace(
                "'--icon=' + icon_path,",
                "'--icon=' + icon_path,\n        '--manifest=app.manifest',                 # Manifeste DPI\n        '--version-file=version.txt',              # Informations de version"
            )
            
            # Sauvegarder le script modifié
            with open('build_native_optimized.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Script de build optimisé créé: build_native_optimized.py")
            return True
        else:
            print("⚠️  Structure du script de build non reconnue")
            return False
            
    except Exception as e:
        print(f"❌ Erreur mise à jour script: {e}")
        return False

def create_version_file():
    """Créer un fichier de version pour PyInstaller"""
    
    print("\n📋 CRÉATION DU FICHIER DE VERSION")
    print("=" * 50)
    
    version_content = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'SOMACA'),
        StringStruct(u'FileDescription', u'Générateur QR & Code à Barre'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'SOMACA_Native'),
        StringStruct(u'LegalCopyright', u'© 2024 SOMACA'),
        StringStruct(u'OriginalFilename', u'SOMACA_Native.exe'),
        StringStruct(u'ProductName', u'Générateur QR & Code à Barre - SOMACA'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
    
    try:
        with open('version.txt', 'w', encoding='utf-8') as f:
            f.write(version_content)
        
        print("✅ Fichier de version créé: version.txt")
        return True
        
    except Exception as e:
        print(f"❌ Erreur création version: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("🎯 CORRECTEUR DE FLOU D'ICÔNE SOMACA")
    print("=" * 60)
    print("Ce script va corriger le problème de flou dans la barre des tâches")
    print()
    
    # Étape 1: Diagnostic
    if not check_icon_quality():
        print("❌ Impossible de continuer sans icône valide")
        return
    
    # Étape 2: Créer une icône optimisée
    if create_high_quality_icon():
        print("✅ Icône optimisée créée")
    
    # Étape 3: Corrections Windows DPI
    if apply_windows_dpi_fix():
        print("✅ Corrections DPI appliquées")
    
    # Étape 4: Fichier de version
    if create_version_file():
        print("✅ Fichier de version créé")
    
    # Étape 5: Script de build optimisé
    if update_build_script():
        print("✅ Script de build optimisé")
    
    print("\n" + "=" * 60)
    print("🎉 CORRECTIONS APPLIQUÉES AVEC SUCCÈS!")
    print()
    print("📋 Prochaines étapes:")
    print("   1. Remplacez qr_icone.ico par qr_icone_optimized.ico")
    print("   2. Utilisez build_native_optimized.py pour compiler")
    print("   3. L'icône devrait être nette dans la barre des tâches")
    print()
    print("💡 Si le problème persiste:")
    print("   - Redémarrez Windows après compilation")
    print("   - Vérifiez les paramètres DPI de Windows")
    print("   - Testez sur différentes résolutions d'écran")

if __name__ == "__main__":
    main()
