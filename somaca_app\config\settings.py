#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Settings - SOMACA Application

Classes de configuration pour centraliser tous les paramètres de l'application.
Utilise le pattern Singleton pour garantir une configuration unique.
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
from .constants import *


@dataclass
class AppSettings:
    """Configuration générale de l'application"""
    
    # Informations de base
    name: str = APP_NAME
    version: str = APP_VERSION
    author: str = APP_AUTHOR
    
    # Chemins
    web_dir: str = "web"
    output_dir: str = "output"
    temp_dir: str = "temp"
    
    # Performance
    max_workers: int = MAX_WORKERS
    cache_size: int = CACHE_SIZE
    
    # Debug
    debug_mode: bool = False
    console_output: bool = False
    
    def __post_init__(self):
        """Initialisation post-création"""
        # Créer les dossiers s'ils n'existent pas
        for directory in [self.output_dir, self.temp_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)


@dataclass 
class ExcelSettings:
    """Configuration pour le traitement Excel"""
    
    # Dimensions des cellules
    row_height: float = EXCEL_ROW_HEIGHT
    header_height: float = EXCEL_HEADER_HEIGHT
    barcode_col_width: int = EXCEL_BARCODE_COL_WIDTH
    qr_col_width: int = EXCEL_QR_COL_WIDTH
    min_col_width: int = EXCEL_MIN_COL_WIDTH
    max_col_width: int = EXCEL_MAX_COL_WIDTH
    
    # Couleurs
    header_color: str = EXCEL_HEADER_COLOR
    header_text_color: str = EXCEL_HEADER_TEXT_COLOR
    alt_row_color: str = EXCEL_ALT_ROW_COLOR
    border_color: str = EXCEL_BORDER_COLOR
    
    # Police
    font_name: str = "Segoe UI"
    font_size: int = 10
    header_font_size: int = 11
    
    # Formats supportés
    supported_formats: List[str] = None
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = SUPPORTED_EXCEL_FORMATS.copy()


@dataclass
class ImageSettings:
    """Configuration pour la génération d'images"""
    
    # DPI et qualité
    default_dpi: int = DEFAULT_DPI
    barcode_dpi: int = BARCODE_DPI
    qr_dpi: int = QR_DPI
    
    # Dimensions codes-barres (pixels)
    barcode_width: int = BARCODE_WIDTH_PX
    barcode_height: int = BARCODE_HEIGHT_PX
    
    # Dimensions QR codes (pixels)
    qr_size: int = QR_SIZE_PX
    qr_border: int = 0  # Sans bordure
    
    # Configuration QR
    qr_error_correction: str = "M"  # L, M, Q, H
    qr_box_size: int = 8
    
    # Configuration codes-barres
    barcode_module_width: float = 0.6
    barcode_module_height: int = 35
    barcode_quiet_zone: int = 1
    
    # Formats supportés
    supported_formats: List[str] = None
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = SUPPORTED_IMAGE_FORMATS.copy()


@dataclass
class RenaultSettings:
    """Configuration spécifique aux étiquettes Renault"""
    
    # Dimensions étiquettes (cm)
    label_width: float = RENAULT_LABEL_WIDTH_CM
    label_height: float = RENAULT_LABEL_HEIGHT_CM
    
    # Dimensions codes-barres Renault (cm)
    barcode_width: float = RENAULT_BARCODE_WIDTH_CM
    barcode_height: float = RENAULT_BARCODE_HEIGHT_CM
    
    # Dimensions QR Renault (cm)
    qr_size: float = RENAULT_QR_SIZE_CM
    
    # Logos
    logo_black_names: List[str] = None
    logo_white_names: List[str] = None
    
    # Couleurs de fond disponibles
    background_colors: List[str] = None
    
    def __post_init__(self):
        if self.logo_black_names is None:
            self.logo_black_names = RENAULT_LOGO_BLACK_NAMES.copy()
        if self.logo_white_names is None:
            self.logo_white_names = RENAULT_LOGO_WHITE_NAMES.copy()
        if self.background_colors is None:
            self.background_colors = ["black", "white"]


@dataclass
class WindowSettings:
    """Configuration de la fenêtre de l'application"""
    
    # Tailles par défaut
    default_width: int = 1200
    default_height: int = 800
    min_width: int = 1000
    min_height: int = 700
    
    # Marges d'écran
    margin_x_large: int = SCREEN_MARGIN_X_LARGE
    margin_x_small: int = SCREEN_MARGIN_X_SMALL
    margin_y_large: int = SCREEN_MARGIN_Y_LARGE
    margin_y_small: int = SCREEN_MARGIN_Y_SMALL
    
    # Tailles adaptatives
    window_sizes: Dict = None
    
    # Options de fenêtre
    resizable: bool = True
    maximized: bool = False
    on_top: bool = False
    shadow: bool = True
    vibrancy: bool = False
    
    def __post_init__(self):
        if self.window_sizes is None:
            self.window_sizes = WINDOW_SIZES.copy()
    
    def get_window_size(self, screen_height: int) -> Tuple[int, int, int, int]:
        """Retourne (width, height, min_width, min_height) selon la résolution"""
        if screen_height <= 768:
            size_config = self.window_sizes['small']
        elif screen_height <= 900:
            size_config = self.window_sizes['medium']
        elif screen_height <= 1080:
            size_config = self.window_sizes['large']
        else:
            size_config = self.window_sizes['xlarge']
        
        return (
            size_config['width'],
            size_config['height'],
            size_config['min_width'],
            size_config['min_height']
        )


# ============================================================================
# SINGLETON PATTERN POUR CONFIGURATION GLOBALE
# ============================================================================

class ConfigManager:
    """Gestionnaire de configuration singleton"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.app = AppSettings()
            self.excel = ExcelSettings()
            self.image = ImageSettings()
            self.renault = RenaultSettings()
            self.window = WindowSettings()
            self._initialized = True
    
    def reload(self):
        """Recharger la configuration"""
        self._initialized = False
        self.__init__()


# Instance globale de configuration
config = ConfigManager()
