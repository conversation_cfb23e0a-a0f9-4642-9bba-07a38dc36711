/* Variables CSS pour les couleurs SOMACA/Renault */
:root {
    --primary-blue: #003366;
    --primary-yellow: #FFD320;
    --secondary-yellow: #FFC107;
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #495057;
    --white: #ffffff;
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--light-gray) 0%, var(--medium-gray) 100%);
    color: var(--dark-gray);
    line-height: 1.6;
    min-height: 100vh;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* En-tête SOMACA */
.header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, #004080 100%);
    color: var(--white);
    padding: 20px 30px;
    box-shadow: 0 4px 20px rgba(0, 51, 102, 0.3);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 30px;
}

.logo-container {
    flex-shrink: 0;
}

.logo {
    background: var(--white);
    color: var(--primary-blue);
    padding: 15px 20px;
    border-radius: 15px;
    border: 3px solid var(--primary-yellow);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(255, 211, 32, 0.3);
}

.logo i {
    font-size: 28px;
}

.header-title h1 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--primary-yellow);
}

.header-title p {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 500;
}

/* Contenu principal */
.main-content {
    flex: 1;
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

/* Cartes */
.card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, #004080 100%);
    color: var(--white);
    padding: 20px 25px;
    border-bottom: 3px solid var(--primary-yellow);
}

.card-header h2 {
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    padding: 25px;
}

/* Groupes d'entrée */
.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--primary-blue);
    font-size: 14px;
}

.file-input-container {
    display: flex;
    gap: 10px;
}

.file-input-container input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid var(--medium-gray);
    border-radius: 8px;
    font-size: 14px;
    background: var(--white);
    transition: border-color 0.3s ease;
}

.file-input-container input:focus {
    outline: none;
    border-color: var(--primary-yellow);
    box-shadow: 0 0 0 3px rgba(255, 211, 32, 0.1);
}

/* Boutons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-yellow) 0%, var(--secondary-yellow) 100%);
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-yellow) 0%, #ffb300 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 211, 32, 0.4);
}

.btn-secondary {
    background: var(--medium-gray);
    color: var(--dark-gray);
    border: 2px solid var(--medium-gray);
}

.btn-secondary:hover {
    background: var(--dark-gray);
    color: var(--white);
    border-color: var(--dark-gray);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: var(--white);
    border: 2px solid #e74c3c;
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.btn-danger:disabled {
    background: #e0e0e0;
    color: #999;
    border: 2px solid #e0e0e0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: var(--white);
    border: 2px solid #27ae60;
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.btn-success:disabled {
    background: #e0e0e0;
    color: #999;
    border: 2px solid #e0e0e0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-renault {
    background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
    color: white !important;
    border: 2px solid #27ae60 !important;
    padding: 15px 25px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    min-height: 55px !important;
    width: 100% !important;
    font-family: 'Segoe UI', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif !important;
}

.btn-renault:hover:not(:disabled) {
    background: linear-gradient(135deg, #229954, #27ae60) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4) !important;
}

.btn-renault:disabled {
    background: #e0e0e0 !important;
    color: #999 !important;
    border: 2px solid #e0e0e0 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.6 !important;
}

.btn-renault:disabled span {
    opacity: 0.4 !important;
    filter: grayscale(100%) !important;
}

.btn-renault i {
    margin-right: 10px !important;
    font-size: 18px !important;
}

/* Conteneur des boutons d'action */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
    width: 100%;
    align-items: stretch;
}

.btn-generate {
    width: 100%;
    padding: 15px 25px;
    font-size: 16px;
    min-height: 55px;
    font-family: 'Segoe UI', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

/* Amélioration de l'affichage des emojis dans les boutons */
.btn-generate, .btn-renault {
    font-feature-settings: "liga" 1, "kern" 1;
    text-rendering: optimizeLegibility;
}

.btn-generate span, .btn-renault span {
    font-family: 'Segoe UI', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
}

.btn-print {
    background: linear-gradient(135deg, var(--primary-yellow) 0%, var(--secondary-yellow) 100%) !important;
    color: var(--primary-blue) !important;
    border: 2px solid var(--primary-blue) !important;
    padding: 15px 25px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(255, 211, 32, 0.3) !important;
    width: 100% !important;
    min-height: 55px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
}

.btn-print:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--secondary-yellow) 0%, #ffb300 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(255, 211, 32, 0.4) !important;
}

.btn-print:disabled {
    background: #e0e0e0 !important;
    color: #999 !important;
    border: 2px solid #e0e0e0 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.6 !important;
}

.btn-print:disabled span {
    opacity: 0.4 !important;
    filter: grayscale(100%) !important;
}

.btn-print i {
    margin-right: 10px !important;
    font-size: 18px !important;
}

/* Bouton Images Encadrées */
.btn-framed {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%) !important;
    color: white !important;
    border: 2px solid #9c27b0 !important;
    padding: 15px 25px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
}

.btn-framed:hover:not(:disabled) {
    background: linear-gradient(135deg, #7b1fa2 0%, #6a1b9a 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(156, 39, 176, 0.4) !important;
}

.btn-framed:disabled {
    background: #e0e0e0 !important;
    color: #999 !important;
    border: 2px solid #e0e0e0 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

.btn-framed i {
    margin-right: 10px !important;
    font-size: 18px !important;
}

.framed-images-options {
    margin-top: 10px;
}

/* Checkboxes */
.checkbox-group {
    margin-bottom: 20px;
}

.checkbox-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.checkbox-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    accent-color: var(--primary-yellow);
}

.checkbox-item label {
    margin: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

/* Boîte d'information */
.info-box {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 2px solid var(--primary-yellow);
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
}

.info-box p {
    margin: 0;
    color: var(--primary-blue);
    font-size: 14px;
    line-height: 1.5;
}

.info-box i {
    color: var(--primary-yellow);
    margin-right: 8px;
}

/* Progression */
.progress-card {
    grid-column: 1 / -1;
}

.progress-container {
    margin-bottom: 15px;
}

.progress-bar {
    width: 100%;
    height: 30px;
    background: var(--medium-gray);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    border: 2px solid var(--primary-blue);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-yellow) 0%, var(--secondary-yellow) 100%);
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 13px;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: var(--primary-blue);
    font-size: 14px;
}

.status-message {
    text-align: center;
    font-weight: 500;
    color: var(--dark-gray);
    font-size: 14px;
    font-family: 'Segoe UI', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
}

.status-message span {
    font-family: 'Segoe UI', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
}

/* Pied de page */
.footer {
    background: var(--primary-blue);
    color: var(--white);
    text-align: center;
    padding: 8px 15px; /* Encore plus réduit : de 12px à 8px */
    font-size: 12px; /* Encore plus réduit : de 13px à 12px */
    font-weight: 500;
    line-height: 1.3; /* Réduire l'espacement des lignes */
}

/* Signature du développeur */
.developer-signature {
    margin-top: 5px; /* Encore plus réduit : de 8px à 5px */
    padding-top: 5px; /* Encore plus réduit : de 8px à 5px */
    border-top: 1px solid rgba(255,255,255,0.2);
}

.developer-signature p {
    margin: 2px 0; /* Encore plus réduit : de 3px à 2px */
    font-size: 10px; /* Encore plus réduit : de 11px à 10px */
    opacity: 0.8;
    line-height: 1.2; /* Réduire l'espacement des lignes */
}

.developer-signature strong {
    color: var(--accent-orange);
    font-weight: 600;
}

.signature {
    font-style: italic;
    opacity: 0.6 !important;
}

/* Modal moderne pour fichier existant */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(3px);
    animation: modalFadeIn 0.2s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-dialog {
    background: var(--white);
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 380px;
    width: 90%;
    overflow: hidden;
    animation: modalSlideIn 0.25s ease-out;
    border: 1px solid #ddd;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-blue), #1e3a8a);
    color: var(--white);
    padding: 20px;
    text-align: center;
    position: relative;
}

.modal-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px auto;
}

.modal-icon i {
    font-size: 24px;
    color: var(--primary-yellow);
}

.modal-header h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.modal-subtitle {
    margin: 0;
    font-size: 12px;
    opacity: 0.9;
    font-weight: 400;
}

.modal-body {
    padding: 20px;
    color: var(--dark-gray);
}

.file-info {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid var(--primary-blue);
}

.file-info i {
    font-size: 32px;
    color: var(--primary-blue);
    margin-right: 15px;
    flex-shrink: 0;
}

.file-details {
    flex: 1;
}

.file-question {
    margin: 0 0 5px 0;
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.filename {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-blue);
    word-break: break-all;
}

.location {
    margin: 0;
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
}

.choice-question {
    text-align: center;
    margin-bottom: 15px;
}

.choice-question h4 {
    margin: 0;
    font-size: 15px;
    color: var(--dark-gray);
    font-weight: 600;
}

.modal-footer {
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
    background: #f8f9fa;
}

.modal-footer .btn {
    width: 100%;
    padding: 15px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 600;
    display: block;
    text-align: left;
}

.modal-footer .btn i {
    font-size: 18px;
    margin-right: 12px;
}

.btn-content {
    display: flex;
    align-items: center;
    color: var(--white);
    gap: 8px;
}

.btn-text {
    text-align: center;
}

.btn-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

.btn-desc {
    display: none;
}

.btn-replace {
    background: #dc3545;
    color: white;
}

.btn-replace:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-rename {
    background: var(--primary-yellow) !important;
    color: var(--dark-gray) !important;
    font-weight: 700 !important;
    border: 2px solid var(--primary-yellow) !important;
}

.btn-rename:hover {
    background: #f1c40f !important;
    color: var(--dark-gray) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.btn-rename .btn-content {
    color: var(--dark-gray) !important;
}

.btn-rename .btn-title {
    color: var(--dark-gray) !important;
    font-weight: 700 !important;
}

.btn-rename i {
    color: var(--dark-gray) !important;
}

.btn-rename .btn-desc {
    color: var(--white) !important;
    opacity: 0.95 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

.btn-rename i {
    color: var(--white) !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #495057;
    transform: translateY(-1px);
}

/* Effet de ripple au clic */
.modal-footer .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.modal-footer .btn:active::before {
    width: 300px;
    height: 300px;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--white);
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 15px 20px;
    border-left: 4px solid var(--primary-yellow);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.notification-message {
    font-weight: 500;
    color: var(--dark-gray);
}

.notification-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: var(--dark-gray);
    padding: 5px;
}

/* Responsive */
@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .main-content {
        padding: 20px 15px;
    }

    .file-input-container {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.6s ease forwards;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }

/* Force la visibilité du bouton d'impression */
button[onclick*="exportImages"],
.btn-print,
button.btn-print,
#btn-print {
    background: linear-gradient(135deg, var(--primary-yellow) 0%, var(--secondary-yellow) 100%) !important;
    color: var(--primary-blue) !important;
    border: 2px solid var(--primary-blue) !important;
    padding: 15px 25px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(255, 211, 32, 0.3) !important;
    width: 100% !important;
    min-height: 55px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    margin: 10px 0 !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 1000 !important;
}

button[onclick*="exportImages"]:hover:not(:disabled),
.btn-print:hover:not(:disabled),
button.btn-print:hover:not(:disabled),
#btn-print:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--secondary-yellow) 0%, #ffb300 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(255, 211, 32, 0.4) !important;
}

button[onclick*="exportImages"]:disabled,
.btn-print:disabled,
button.btn-print:disabled,
#btn-print:disabled {
    background: #e0e0e0 !important;
    color: #999 !important;
    border: 2px solid #e0e0e0 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Style spécial pour le bouton désactivé */
#btn-print:disabled {
    background: linear-gradient(135deg, #cccccc, #999999) !important;
    color: #666666 !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Modal d'impression élégante et moderne */
.modal-elegant {
    max-width: 420px;
    width: 90%;
    background: var(--white);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 51, 102, 0.15);
    overflow: hidden;
    border: none;
    position: relative;
}

.modal-elegant::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-yellow));
}

.modal-header-elegant {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 25px 30px 20px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
}

.modal-icon-elegant {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-blue), #004080);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    box-shadow: 0 8px 25px rgba(0, 51, 102, 0.2);
}

.modal-icon-elegant i {
    font-size: 1.8em;
    color: var(--white);
}

.modal-header-elegant h3 {
    color: var(--primary-blue);
    font-size: 1.4em;
    font-weight: 700;
    margin: 0 0 5px;
    letter-spacing: -0.5px;
}

.modal-subtitle-elegant {
    color: #6c757d;
    font-size: 0.95em;
    margin: 0;
    font-weight: 400;
}

.modal-content-elegant {
    padding: 20px 30px 10px;
    border-bottom: 1px solid #e9ecef;
}

.modal-content-elegant .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 0;
}

.modal-content-elegant .checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 10px;
    transition: all 0.2s ease;
    border: 2px solid #e9ecef;
    background: #f8f9fa;
}

.modal-content-elegant .checkbox-label:hover {
    background: #e9ecef;
    border-color: var(--primary-blue);
}

.modal-content-elegant .checkbox-label input[type="checkbox"] {
    margin: 0 12px 0 0;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-blue);
}

.modal-content-elegant .checkbox-text {
    font-weight: 500;
    color: var(--dark-gray);
    font-size: 0.95em;
}

.modal-content-elegant .checkmark {
    display: none;
}

.modal-buttons-elegant {
    padding: 30px 25px 25px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Boutons élégants */
.btn-elegant {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border: 2px solid #e9ecef;
    border-radius: 14px;
    background: var(--white);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    font-family: 'Segoe UI', sans-serif;
    position: relative;
    overflow: hidden;
}

.btn-elegant::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-elegant:hover::before {
    left: 100%;
}

.btn-icon-elegant {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    transition: all 0.3s ease;
}

.btn-icon-elegant i {
    font-size: 1.3em;
    transition: transform 0.3s ease;
}

.btn-text-elegant {
    flex: 1;
    text-align: left;
}

.btn-title-elegant {
    display: block;
    font-size: 1em;
    font-weight: 600;
    margin-bottom: 2px;
    transition: color 0.3s ease;
}

.btn-desc-elegant {
    display: block;
    font-size: 0.8em;
    opacity: 0.7;
    font-weight: 400;
    transition: opacity 0.3s ease;
}

/* Couleurs spécifiques pour chaque bouton */
.btn-export-elegant {
    border-color: #e3f2fd;
}

.btn-export-elegant .btn-icon-elegant {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: var(--white);
}

.btn-export-elegant .btn-title-elegant {
    color: #1976d2;
}

.btn-export-elegant:hover {
    border-color: #2196f3;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.15);
}

.btn-print-elegant {
    border-color: #e8f5e8;
}

.btn-print-elegant .btn-icon-elegant {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    color: var(--white);
}

.btn-print-elegant .btn-title-elegant {
    color: #388e3c;
}

.btn-print-elegant:hover {
    border-color: #4caf50;
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.15);
}

.btn-framed-elegant {
    border-color: #fff3e0;
}

.btn-framed-elegant .btn-icon-elegant {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: var(--white);
}

.btn-framed-elegant .btn-title-elegant {
    color: #f57c00;
}

.btn-framed-elegant:hover {
    border-color: #ff9800;
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 152, 0, 0.15);
}

.btn-cancel-elegant {
    border-color: #ffebee;
}

.btn-cancel-elegant .btn-icon-elegant {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: var(--white);
}

.btn-cancel-elegant .btn-title-elegant {
    color: #d32f2f;
}

.btn-cancel-elegant:hover {
    border-color: #f44336;
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(244, 67, 54, 0.15);
}

.btn-elegant:hover .btn-icon-elegant i {
    transform: scale(1.1);
}

.btn-elegant:hover .btn-desc-elegant {
    opacity: 1;
}

/* Animation d'entrée */
.modal-elegant {
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive */
@media (max-width: 480px) {
    .modal-elegant {
        max-width: 95%;
        margin: 20px auto;
    }

    .modal-header-elegant {
        padding: 20px 20px 15px;
    }

    .modal-buttons-elegant {
        padding: 20px 15px;
    }

    .btn-elegant {
        padding: 14px 16px;
    }

    .btn-icon-elegant {
        width: 40px;
        height: 40px;
        margin-right: 12px;
    }
}

/* Fenêtre stable - contenu adaptatif sans défilement */
body, html {
    overflow: hidden; /* Aucun défilement du tout */
    height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
}

.app-container {
    height: 100vh; /* Exactement la hauteur de la fenêtre */
    width: 100vw; /* Exactement la largeur de la fenêtre */
    overflow: hidden; /* Aucun défilement */
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

/* Header adaptatif */
.header {
    flex-shrink: 0; /* Ne se réduit jamais */
    min-height: 60px; /* Hauteur minimale */
    max-height: 100px; /* Hauteur maximale */
    overflow: hidden;
}

/* Contenu principal adaptatif */
.main-content {
    flex: 1; /* Prend tout l'espace disponible */
    overflow: hidden; /* Aucun défilement */
    display: flex;
    flex-direction: column;
    min-height: 0; /* Important pour flex */
}

/* Footer adaptatif */
.footer {
    flex-shrink: 0; /* Ne se réduit jamais */
    min-height: 25px; /* Hauteur minimale */
    max-height: 50px; /* Hauteur maximale */
    overflow: hidden;
}

/* Grille de contenu adaptative */
.content-grid {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: min(25px, 2vh); /* Gap adaptatif */
    margin-bottom: min(25px, 2vh);
    overflow: hidden;
    min-height: 0;
}

/* Cartes adaptatives */
.card {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
}

.card-body {
    flex: 1;
    overflow: hidden;
    padding: min(25px, 2vh); /* Padding adaptatif */
    display: flex;
    flex-direction: column;
}

/* Carte de progression adaptative */
.progress-card {
    flex-shrink: 0;
    min-height: 80px;
    max-height: 120px;
    overflow: hidden;
}

.progress-card .card-body {
    padding: min(20px, 1.5vh);
}

/* Groupes de formulaire adaptatifs */
.form-group {
    margin-bottom: min(20px, 1.5vh);
    flex-shrink: 0;
}

/* Boutons adaptatifs */
.action-buttons {
    margin-top: auto; /* Pousse vers le bas */
    flex-shrink: 0;
}

.btn {
    padding: min(15px, 1vh) min(25px, 2vw);
    font-size: min(16px, 1.2vw);
    min-height: min(55px, 5vh);
}

/* Adaptation pour très petites fenêtres */
@media (max-height: 600px) {
    .content-grid {
        grid-template-columns: 1fr; /* Une seule colonne */
        gap: 10px;
        margin-bottom: 10px;
    }

    .card-body {
        padding: 10px;
    }

    .form-group {
        margin-bottom: 8px;
    }

    .btn {
        padding: 8px 15px;
        font-size: 14px;
        min-height: 35px;
    }

    .header {
        min-height: 40px;
        max-height: 60px;
    }

    .footer {
        min-height: 20px;
        max-height: 30px;
    }
}

/* Optimisation pour petites fenêtres */
@media (max-height: 750px) {
    .header {
        padding: 15px 30px; /* Réduire le padding du header */
    }

    .main-content {
        padding: 20px 30px; /* Réduire le padding du contenu */
    }

    .card-body {
        padding: 20px; /* Réduire le padding des cartes */
    }

    .form-group {
        margin-bottom: 15px; /* Réduire l'espacement entre les groupes */
    }

    .footer {
        padding: 5px 15px; /* Ultra-compact pour les petites fenêtres */
        font-size: 11px; /* Encore plus petit */
    }

    .developer-signature {
        margin-top: 3px;
        padding-top: 3px;
    }

    .developer-signature p {
        margin: 1px 0;
        font-size: 9px; /* Très petit pour économiser l'espace */
    }
}


