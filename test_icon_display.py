#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour tester l'affichage de l'icône dans la barre des tâches
"""

import webview
import os
import time

def test_icon_display():
    """Tester l'affichage de l'icône dans une fenêtre WebView"""
    
    print("🎯 TEST D'AFFICHAGE DE L'ICÔNE SOMACA")
    print("=" * 50)
    
    # Chemin vers l'icône
    icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'qr_icone.ico')
    
    # Vérifier que l'icône existe
    if not os.path.exists(icon_path):
        print(f"❌ Icône non trouvée: {icon_path}")
        return False
    
    print(f"✅ Icône trouvée: {icon_path}")
    
    # Créer une page HTML simple pour le test
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Icône SOMACA</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                text-align: center;
                padding: 50px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                margin: 0;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 30px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
                max-width: 500px;
                margin: 0 auto;
            }
            h1 {
                margin-bottom: 20px;
                font-size: 2.5em;
            }
            .icon-test {
                font-size: 1.2em;
                margin: 20px 0;
                padding: 15px;
                background: rgba(255,255,255,0.2);
                border-radius: 10px;
            }
            .instructions {
                margin-top: 30px;
                font-size: 1.1em;
                line-height: 1.6;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎯 Test Icône SOMACA</h1>
            
            <div class="icon-test">
                <strong>✅ Icône chargée avec succès!</strong>
            </div>
            
            <div class="instructions">
                <p><strong>Instructions de test :</strong></p>
                <p>1. Regardez la barre des tâches Windows</p>
                <p>2. L'icône doit être nette et claire</p>
                <p>3. Pas de flou ou de pixellisation</p>
                <p>4. Fermez cette fenêtre quand terminé</p>
            </div>
            
            <div style="margin-top: 30px; font-size: 0.9em; opacity: 0.8;">
                Générateur QR & Code à Barre - SOMACA<br>
                Test d'affichage d'icône
            </div>
        </div>
    </body>
    </html>
    """
    
    # Créer un fichier HTML temporaire
    temp_html = 'test_icon_temp.html'
    with open(temp_html, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    try:
        print("🚀 Lancement de la fenêtre de test...")
        print("👀 Vérifiez l'icône dans la barre des tâches!")
        
        # Créer la fenêtre avec l'icône
        window = webview.create_window(
            'Test Icône SOMACA',
            temp_html,
            width=600,
            height=500,
            resizable=True,
            icon=icon_path  # L'icône à tester
        )
        
        # Démarrer WebView
        webview.start(debug=False)
        
        print("✅ Test terminé!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False
        
    finally:
        # Nettoyer le fichier temporaire
        try:
            if os.path.exists(temp_html):
                os.remove(temp_html)
        except:
            pass

def main():
    """Fonction principale"""
    
    print("Appuyez sur Entrée pour commencer le test...")
    input()
    
    success = test_icon_display()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ Test d'icône terminé!")
        print("\n💡 Si l'icône était floue:")
        print("   1. Vérifiez que qr_icone.ico contient toutes les tailles")
        print("   2. Assurez-vous que l'icône est bien définie dans webview.create_window()")
        print("   3. Redémarrez l'application après modification")
    else:
        print("\n❌ Échec du test d'icône!")

if __name__ == "__main__":
    main()
