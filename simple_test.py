#!/usr/bin/env python3
"""
Test simple de webview
"""

import os
import webview

def main():
    print("Test simple webview...")
    
    # Chemin vers le dossier web
    web_dir = os.path.join(os.path.dirname(__file__), 'web')
    index_path = os.path.join(web_dir, 'index.html')
    
    print(f"Web dir: {web_dir}")
    print(f"Index: {index_path}")
    print(f"Index existe: {os.path.exists(index_path)}")
    
    try:
        # Créer une fenêtre simple
        window = webview.create_window(
            'Test Simple',
            url=index_path,
            width=800,
            height=600
        )
        
        print("Fenêtre créée, lancement...")
        webview.start(debug=True)
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
