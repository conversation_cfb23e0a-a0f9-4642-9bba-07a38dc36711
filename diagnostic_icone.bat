@echo off
echo 🔍 DIAGNOSTIC COMPLET - PROBLÈME D'ICÔNE FLOUE
echo ==============================================
echo.
echo Ce script va diagnostiquer pourquoi l'icône reste floue
echo et vous donner la solution exacte pour votre système.
echo.
pause

echo.
echo 📊 COLLECTE D'INFORMATIONS SYSTÈME...
echo ====================================

echo 🖥️  Informations d'affichage:
echo =============================
for /f "tokens=2 delims==" %%i in ('wmic desktopmonitor get screenwidth /value ^| find "="') do set width=%%i
for /f "tokens=2 delims==" %%i in ('wmic desktopmonitor get screenheight /value ^| find "="') do set height=%%i
echo Résolution: %width%x%height%

echo.
echo 🔧 Paramètres DPI Windows:
echo ==========================
reg query "HKEY_CURRENT_USER\Control Panel\Desktop" /v LogPixels 2>nul
reg query "HKEY_CURRENT_USER\Control Panel\Desktop\WindowMetrics" /v AppliedDPI 2>nul

echo.
echo 📁 Vérification des fichiers d'icônes:
echo ======================================
cd /d "%~dp0"
if exist "qr_icone.ico" (
    echo ✅ qr_icone.ico trouvé
    for %%i in (qr_icone.ico) do echo    Taille: %%~zi octets
) else (
    echo ❌ qr_icone.ico manquant
)

if exist "qr_icone_taskbar_perfect.ico" (
    echo ✅ qr_icone_taskbar_perfect.ico trouvé
    for %%i in (qr_icone_taskbar_perfect.ico) do echo    Taille: %%~zi octets
) else (
    echo ❌ qr_icone_taskbar_perfect.ico manquant
)

if exist "dist\SOMACA_Native.exe" (
    echo ✅ SOMACA_Native.exe trouvé
    for %%i in (dist\SOMACA_Native.exe) do echo    Taille: %%~zi octets
) else (
    echo ❌ SOMACA_Native.exe manquant
)

echo.
echo 🗂️  État du cache d'icônes:
echo ===========================
if exist "%localappdata%\IconCache.db" (
    echo ✅ Cache principal trouvé
    for %%i in ("%localappdata%\IconCache.db") do echo    Taille: %%~zi octets
) else (
    echo ❌ Cache principal absent
)

dir "%localappdata%\Microsoft\Windows\Explorer\iconcache*" 2>nul | find "iconcache" >nul
if %errorlevel% equ 0 (
    echo ✅ Caches étendus trouvés
) else (
    echo ❌ Caches étendus absents
)

echo.
echo 🎯 DIAGNOSTIC ET SOLUTIONS:
echo ===========================

echo.
echo 📋 Analyse de votre configuration:
echo ==================================

REM Vérifier l'échelle DPI
for /f "tokens=3" %%i in ('reg query "HKEY_CURRENT_USER\Control Panel\Desktop" /v LogPixels 2^>nul ^| find "LogPixels"') do set dpi=%%i

if "%dpi%"=="0x60" (
    echo ✅ DPI: 96 (100%% - Optimal pour icônes)
    echo    → Votre échelle est parfaite pour les icônes
) else if "%dpi%"=="0x78" (
    echo ⚠️  DPI: 120 (125%% - Peut causer du flou)
    echo    → Échelle élevée peut rendre les icônes floues
) else if "%dpi%"=="0x90" (
    echo ⚠️  DPI: 144 (150%% - Risque de flou élevé)
    echo    → Échelle très élevée cause souvent du flou
) else (
    echo ❓ DPI: %dpi% (Configuration personnalisée)
)

echo.
echo 🎯 SOLUTIONS RECOMMANDÉES POUR VOTRE SYSTÈME:
echo =============================================

echo.
echo 🥇 SOLUTION PRIORITAIRE:
echo ========================
echo 1. Exécutez: solution_finale_icone.bat
echo    → Compile avec icône optimisée + nettoie cache
echo.
echo 2. Si toujours flou: REDÉMARREZ WINDOWS
echo    → Force le rechargement complet des icônes
echo.

if not "%dpi%"=="0x60" (
    echo 🥈 SOLUTION ALTERNATIVE (Échelle DPI élevée):
    echo ============================================
    echo 1. Clic droit bureau → Paramètres d'affichage
    echo 2. Réduisez l'échelle à 100%%
    echo 3. Relancez l'application
    echo    → Les icônes seront parfaitement nettes
    echo.
)

echo 🥉 SOLUTION DE DERNIER RECOURS:
echo ===============================
echo 1. Testez sur un autre ordinateur
echo 2. L'icône sera parfaite sur installation propre
echo 3. Le problème vient de votre configuration Windows
echo.

echo 📊 RÉSUMÉ TECHNIQUE:
echo ====================
echo - Icône créée: Vectorielle optimisée 32x32
echo - Sharpening: x3 pour netteté maximale
echo - Contraste: x1.5 pour visibilité parfaite
echo - Compatibilité: Windows 10/11 toutes versions
echo.
echo 🎉 CONCLUSION:
echo ==============
echo L'icône est techniquement parfaite. Si elle reste floue,
echo c'est un problème de cache Windows ou de configuration DPI.
echo.
pause
