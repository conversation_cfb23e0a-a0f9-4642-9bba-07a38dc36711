#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logger Configuration - SOMACA

Configuration centralisée du système de logging pour l'application.
Remplace tous les print() par un système de logging professionnel.
"""

import os
import sys
import logging
import logging.handlers
from datetime import datetime
from typing import Optional
from pathlib import Path


class ColoredFormatter(logging.Formatter):
    """Formateur coloré pour la console"""
    
    # Codes couleur ANSI
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Vert
        'WARNING': '\033[33m',  # <PERSON><PERSON>ne
        'ERROR': '\033[31m',    # Rouge
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Ajouter la couleur selon le niveau
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class SomacaLogger:
    """
    Gestionnaire de logging centralisé pour SOMACA.
    
    Fournit un logging structuré avec rotation des fichiers,
    niveaux configurables et formatage professionnel.
    """
    
    def __init__(self, name: str = "SOMACA", log_dir: str = "logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.logger = None
        self._setup_logging()
    
    def _setup_logging(self):
        """Configurer le système de logging"""
        # Créer le dossier de logs s'il n'existe pas
        self.log_dir.mkdir(exist_ok=True)
        
        # Créer le logger principal
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # Éviter la duplication des handlers
        if self.logger.handlers:
            return
        
        # Format pour les logs
        file_format = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        console_format = ColoredFormatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # Handler pour fichier principal avec rotation
        main_log_file = self.log_dir / f"{self.name.lower()}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_format)
        
        # Handler pour erreurs séparées
        error_log_file = self.log_dir / f"{self.name.lower()}_errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=5*1024*1024,   # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_format)
        
        # Handler pour console (seulement si pas en mode production)
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(console_format)
        
        # Ajouter les handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        
        # Console seulement en mode debug ou si explicitement demandé
        if self._should_log_to_console():
            self.logger.addHandler(console_handler)
        
        # Log de démarrage
        self.logger.info(f"=== {self.name} Logger initialisé ===")
        self.logger.info(f"Dossier de logs: {self.log_dir.absolute()}")
    
    def _should_log_to_console(self) -> bool:
        """Déterminer si on doit logger vers la console"""
        # En mode debug
        if os.getenv('SOMACA_DEBUG', '').lower() in ('1', 'true', 'yes'):
            return True
        
        # Si explicitement demandé
        if os.getenv('SOMACA_CONSOLE_LOG', '').lower() in ('1', 'true', 'yes'):
            return True
        
        # En développement (si on détecte un environnement de dev)
        if os.path.exists('.git') or os.path.exists('requirements.txt'):
            return True
        
        return False
    
    def get_logger(self, module_name: str = None) -> logging.Logger:
        """
        Obtenir un logger pour un module spécifique.
        
        Args:
            module_name: Nom du module
            
        Returns:
            Logger configuré
        """
        if module_name:
            logger_name = f"{self.name}.{module_name}"
        else:
            logger_name = self.name
        
        return logging.getLogger(logger_name)
    
    def set_level(self, level: str):
        """
        Définir le niveau de logging.
        
        Args:
            level: Niveau (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        numeric_level = getattr(logging, level.upper(), None)
        if not isinstance(numeric_level, int):
            raise ValueError(f'Niveau de log invalide: {level}')
        
        self.logger.setLevel(numeric_level)
        self.logger.info(f"Niveau de logging changé vers: {level}")
    
    def log_performance(self, operation: str, duration: float, details: str = ""):
        """
        Logger les performances d'une opération.
        
        Args:
            operation: Nom de l'opération
            duration: Durée en secondes
            details: Détails supplémentaires
        """
        perf_logger = self.get_logger("performance")
        message = f"PERF | {operation} | {duration:.3f}s"
        if details:
            message += f" | {details}"
        perf_logger.info(message)
    
    def log_user_action(self, action: str, details: dict = None):
        """
        Logger une action utilisateur.
        
        Args:
            action: Action effectuée
            details: Détails de l'action
        """
        user_logger = self.get_logger("user")
        message = f"USER | {action}"
        if details:
            message += f" | {details}"
        user_logger.info(message)
    
    def log_file_operation(self, operation: str, file_path: str, success: bool, details: str = ""):
        """
        Logger une opération sur fichier.
        
        Args:
            operation: Type d'opération (read, write, delete, etc.)
            file_path: Chemin du fichier
            success: Succès de l'opération
            details: Détails supplémentaires
        """
        file_logger = self.get_logger("files")
        status = "SUCCESS" if success else "FAILED"
        message = f"FILE | {operation.upper()} | {status} | {file_path}"
        if details:
            message += f" | {details}"
        
        if success:
            file_logger.info(message)
        else:
            file_logger.error(message)
    
    def log_generation_progress(self, current: int, total: int, operation: str = ""):
        """
        Logger le progrès d'une génération.
        
        Args:
            current: Élément actuel
            total: Total d'éléments
            operation: Type d'opération
        """
        progress_logger = self.get_logger("progress")
        percentage = (current / total * 100) if total > 0 else 0
        message = f"PROGRESS | {current}/{total} ({percentage:.1f}%)"
        if operation:
            message += f" | {operation}"
        progress_logger.debug(message)
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """
        Nettoyer les anciens fichiers de log.
        
        Args:
            days_to_keep: Nombre de jours à conserver
        """
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            cleaned_count = 0
            for log_file in self.log_dir.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.info(f"Nettoyage logs: {cleaned_count} fichiers supprimés")
        
        except Exception as e:
            self.logger.error(f"Erreur nettoyage logs: {e}")


# ============================================================================
# CONFIGURATION GLOBALE
# ============================================================================

# Instance globale du logger
_somaca_logger = None

def get_logger(module_name: str = None) -> logging.Logger:
    """
    Obtenir le logger global de l'application.
    
    Args:
        module_name: Nom du module (optionnel)
        
    Returns:
        Logger configuré
    """
    global _somaca_logger
    
    if _somaca_logger is None:
        _somaca_logger = SomacaLogger()
    
    return _somaca_logger.get_logger(module_name)

def setup_logging(log_dir: str = "logs", level: str = "INFO"):
    """
    Configurer le logging global de l'application.
    
    Args:
        log_dir: Dossier des logs
        level: Niveau de logging
    """
    global _somaca_logger
    _somaca_logger = SomacaLogger(log_dir=log_dir)
    _somaca_logger.set_level(level)

def log_performance(operation: str, duration: float, details: str = ""):
    """Raccourci pour logger les performances"""
    global _somaca_logger
    if _somaca_logger:
        _somaca_logger.log_performance(operation, duration, details)

def log_user_action(action: str, details: dict = None):
    """Raccourci pour logger les actions utilisateur"""
    global _somaca_logger
    if _somaca_logger:
        _somaca_logger.log_user_action(action, details)

def log_file_operation(operation: str, file_path: str, success: bool, details: str = ""):
    """Raccourci pour logger les opérations fichier"""
    global _somaca_logger
    if _somaca_logger:
        _somaca_logger.log_file_operation(operation, file_path, success, details)
