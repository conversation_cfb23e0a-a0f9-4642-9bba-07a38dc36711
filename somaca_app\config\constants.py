#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Constants - SOMACA Application

Toutes les constantes utilisées dans l'application sont centralisées ici.
Cela facilite la maintenance et évite la duplication de valeurs magiques.
"""

import os

# ============================================================================
# CONSTANTES GÉNÉRALES
# ============================================================================

# Informations de l'application
APP_NAME = "Générateur QR & Code à Barre - SOMACA"
APP_VERSION = "3.0.0"
APP_AUTHOR = "Imad Elberrouagui"

# ============================================================================
# CONSTANTES D'IMAGES ET CODES
# ============================================================================

# DPI et qualité d'image
DEFAULT_DPI = 300
BARCODE_DPI = 96
QR_DPI = 96

# Dimensions des codes-barres (en pixels à 96 DPI)
BARCODE_WIDTH_PX = 166  # 4.4cm à 96 DPI
BARCODE_HEIGHT_PX = 83  # 2.2cm à 96 DPI

# Dimensions des QR codes (en pixels à 96 DPI)
QR_SIZE_PX = 95  # 2.5cm à 96 DPI

# Dimensions étiquettes Renault
RENAULT_LABEL_WIDTH_CM = 7.0
RENAULT_LABEL_HEIGHT_CM = 3.9
RENAULT_BARCODE_WIDTH_CM = 4.5
RENAULT_BARCODE_HEIGHT_CM = 3.0
RENAULT_QR_SIZE_CM = 2.0

# ============================================================================
# CONSTANTES EXCEL
# ============================================================================

# Dimensions des cellules Excel (en points)
EXCEL_ROW_HEIGHT = 56.7  # 2cm en points (1cm = 28.35 points)
EXCEL_HEADER_HEIGHT = 30
EXCEL_BARCODE_COL_WIDTH = 18
EXCEL_QR_COL_WIDTH = 12
EXCEL_MIN_COL_WIDTH = 10
EXCEL_MAX_COL_WIDTH = 40

# Couleurs Excel
EXCEL_HEADER_COLOR = "003366"
EXCEL_HEADER_TEXT_COLOR = "FFFFFF"
EXCEL_ALT_ROW_COLOR = "F8F9FA"
EXCEL_BORDER_COLOR = "CCCCCC"

# ============================================================================
# CONSTANTES DE PERFORMANCE
# ============================================================================

# Cache
CACHE_SIZE = 1000
BARCODE_CACHE_SIZE = 500
QR_CACHE_SIZE = 500

# Threading
MAX_WORKERS = min(8, (os.cpu_count() or 1) + 4)

# ============================================================================
# CONSTANTES DE FICHIERS
# ============================================================================

# Extensions supportées
SUPPORTED_EXCEL_FORMATS = ['.xlsx', '.xls', '.csv']
SUPPORTED_IMAGE_FORMATS = ['.png', '.jpg', '.jpeg', '.bmp']

# Noms de fichiers par défaut
DEFAULT_OUTPUT_SUFFIX = "_avec_codes"
DEFAULT_RENAULT_SUFFIX = "_etiquettes_renault"

# ============================================================================
# CONSTANTES RENAULT
# ============================================================================

# Logos Renault
RENAULT_LOGO_BLACK_NAMES = [
    "renault_logo noir.png",
    "renault_noir.jpg", 
    "renault_noir.png",
    "renault-logo.png"
]

RENAULT_LOGO_WHITE_NAMES = [
    "renault-logo blach.jpg",
    "renault_blanc.jpg",
    "renault_blanc.png", 
    "renault-logo.png"
]

# ============================================================================
# CONSTANTES D'INTERFACE
# ============================================================================

# Tailles de fenêtre selon résolution écran
WINDOW_SIZES = {
    'small': {  # <= 768p
        'width': 950, 'height': 550,
        'min_width': 850, 'min_height': 500
    },
    'medium': {  # <= 900p
        'width': 1050, 'height': 620,
        'min_width': 950, 'min_height': 570
    },
    'large': {  # <= 1080p
        'width': 1150, 'height': 720,
        'min_width': 1050, 'min_height': 620
    },
    'xlarge': {  # > 1080p
        'width': 1250, 'height': 820,
        'min_width': 1150, 'min_height': 670
    }
}

# Marges d'écran
SCREEN_MARGIN_X_LARGE = 100
SCREEN_MARGIN_X_SMALL = 80
SCREEN_MARGIN_Y_LARGE = 120
SCREEN_MARGIN_Y_SMALL = 100

# ============================================================================
# CONSTANTES DE NETTOYAGE DE TEXTE
# ============================================================================

# Remplacements de caractères accentués pour codes-barres
ACCENT_REPLACEMENTS = {
    'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ä': 'a', 'å': 'a',
    'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e',
    'ì': 'i', 'í': 'i', 'î': 'i', 'ï': 'i',
    'ò': 'o', 'ó': 'o', 'ô': 'o', 'õ': 'o', 'ö': 'o',
    'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u',
    'ý': 'y', 'ÿ': 'y',
    'ç': 'c', 'ñ': 'n',
    'À': 'A', 'Á': 'A', 'Â': 'A', 'Ã': 'A', 'Ä': 'A', 'Å': 'A',
    'È': 'E', 'É': 'E', 'Ê': 'E', 'Ë': 'E',
    'Ì': 'I', 'Í': 'I', 'Î': 'I', 'Ï': 'I',
    'Ò': 'O', 'Ó': 'O', 'Ô': 'O', 'Õ': 'O', 'Ö': 'O',
    'Ù': 'U', 'Ú': 'U', 'Û': 'U', 'Ü': 'U',
    'Ý': 'Y', 'Ÿ': 'Y',
    'Ç': 'C', 'Ñ': 'N'
}

# Caractères de séparation problématiques pour QR codes
QR_PROBLEMATIC_CHARS = {
    '|': ' - ',
    '\n': ' ',
    '\r': ' ',
    '\t': ' '
}
