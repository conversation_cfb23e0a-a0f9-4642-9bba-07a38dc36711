#!/usr/bin/env python3
"""
Script de test pour diagnostiquer les problèmes de lancement
"""

import sys
import os
import traceback

def test_imports():
    """Tester tous les imports nécessaires"""
    print("=== TEST DES IMPORTS ===")
    
    try:
        import webview
        print("✅ webview importé")
    except Exception as e:
        print(f"❌ Erreur import webview: {e}")
        return False
    
    try:
        import tkinter
        print("✅ tkinter importé")
    except Exception as e:
        print(f"❌ Erreur import tkinter: {e}")
        return False
    
    try:
        from app_native import Api, QRCodeGenerator
        print("✅ Classes app_native importées")
    except Exception as e:
        print(f"❌ Erreur import app_native: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_files():
    """Tester l'existence des fichiers"""
    print("\n=== TEST DES FICHIERS ===")
    
    web_dir = os.path.join(os.path.dirname(__file__), 'web')
    print(f"Répertoire web: {web_dir}")
    
    if not os.path.exists(web_dir):
        print(f"❌ Répertoire web manquant: {web_dir}")
        return False
    print("✅ Répertoire web existe")
    
    index_path = os.path.join(web_dir, 'index.html')
    if not os.path.exists(index_path):
        print(f"❌ index.html manquant: {index_path}")
        return False
    print("✅ index.html existe")
    
    return True

def test_screen_detection():
    """Tester la détection d'écran"""
    print("\n=== TEST DÉTECTION ÉCRAN ===")
    
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        root.destroy()
        print(f"✅ Écran détecté: {screen_width}x{screen_height}")
        return True
    except Exception as e:
        print(f"❌ Erreur détection écran: {e}")
        traceback.print_exc()
        return False

def test_webview_creation():
    """Tester la création d'une fenêtre webview simple"""
    print("\n=== TEST CRÉATION WEBVIEW ===")
    
    try:
        import webview
        from app_native import Api
        
        web_dir = os.path.join(os.path.dirname(__file__), 'web')
        api = Api()
        
        window = webview.create_window(
            'Test SOMACA',
            url=os.path.join(web_dir, 'index.html'),
            js_api=api,
            width=800,
            height=600,
            resizable=True
        )
        
        print("✅ Fenêtre webview créée")
        print("Lancement de webview...")
        
        # Lancer avec debug
        webview.start(debug=True)
        
    except Exception as e:
        print(f"❌ Erreur création webview: {e}")
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("=== DIAGNOSTIC LANCEMENT APPLICATION SOMACA ===")
    print(f"Python: {sys.version}")
    print(f"Répertoire: {os.getcwd()}")
    
    # Tests séquentiels
    if not test_imports():
        print("\n❌ ÉCHEC: Problème d'imports")
        return
    
    if not test_files():
        print("\n❌ ÉCHEC: Problème de fichiers")
        return
    
    if not test_screen_detection():
        print("\n❌ ÉCHEC: Problème détection écran")
        return
    
    print("\n✅ TOUS LES TESTS PRÉLIMINAIRES RÉUSSIS")
    print("Tentative de lancement de webview...")
    
    test_webview_creation()

if __name__ == '__main__':
    main()
