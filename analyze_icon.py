#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour analyser l'icône qr_icone.ico et vérifier sa qualité
"""

import os
from PIL import Image
import struct

def analyze_ico_file(ico_path):
    """Analyser un fichier .ico et extraire les informations"""
    
    if not os.path.exists(ico_path):
        print(f"❌ Fichier non trouvé: {ico_path}")
        return False
    
    print(f"🔍 Analyse de l'icône: {ico_path}")
    print("=" * 60)
    
    try:
        # Ouvrir le fichier ICO
        with open(ico_path, 'rb') as f:
            # Lire l'en-tête ICO
            header = f.read(6)
            if len(header) < 6:
                print("❌ Fichier ICO invalide (en-tête trop court)")
                return False
            
            # Décoder l'en-tête
            reserved, type_ico, count = struct.unpack('<HHH', header)
            
            if reserved != 0 or type_ico != 1:
                print("❌ Fichier ICO invalide (signature incorrecte)")
                return False
            
            print(f"📊 Nombre d'images dans l'ICO: {count}")
            print()
            
            # Lire les entrées d'images
            images_info = []
            for i in range(count):
                entry = f.read(16)
                if len(entry) < 16:
                    print(f"❌ Entrée {i+1} invalide")
                    continue
                
                width, height, colors, reserved, planes, bpp, size, offset = struct.unpack('<BBBBHHLL', entry)
                
                # Correction pour les tailles 256x256 (stockées comme 0)
                if width == 0:
                    width = 256
                if height == 0:
                    height = 256
                
                images_info.append({
                    'index': i + 1,
                    'width': width,
                    'height': height,
                    'colors': colors,
                    'bpp': bpp,
                    'size': size,
                    'offset': offset
                })
            
            # Afficher les informations de chaque image
            print("📋 Détails des images:")
            print("-" * 60)
            for info in images_info:
                print(f"Image {info['index']:2d}: {info['width']:3d}x{info['height']:3d} px, "
                      f"{info['bpp']:2d} bits/pixel, {info['size']:6d} bytes")
            
            print()
            
            # Vérifier les tailles recommandées
            recommended_sizes = [16, 32, 48, 64, 128, 256]
            available_sizes = [info['width'] for info in images_info]
            
            print("✅ Tailles recommandées présentes:")
            print("-" * 40)
            for size in recommended_sizes:
                if size in available_sizes:
                    print(f"✅ {size:3d}x{size:3d} px - PRÉSENT")
                else:
                    print(f"❌ {size:3d}x{size:3d} px - MANQUANT")
            
            print()
            
            # Évaluation globale
            score = len([s for s in recommended_sizes if s in available_sizes])
            total = len(recommended_sizes)
            percentage = (score / total) * 100
            
            print(f"📊 Score de qualité: {score}/{total} ({percentage:.1f}%)")
            
            if percentage >= 80:
                print("🎉 Excellente icône! Toutes les tailles importantes sont présentes.")
            elif percentage >= 60:
                print("👍 Bonne icône, mais quelques tailles manquent.")
            elif percentage >= 40:
                print("⚠️  Icône correcte, mais plusieurs tailles importantes manquent.")
            else:
                print("❌ Icône de qualité insuffisante. Beaucoup de tailles manquent.")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        return False

def test_icon_with_pil(ico_path):
    """Tester l'icône avec PIL pour vérifier la compatibilité"""
    
    print("\n🖼️  Test de compatibilité PIL:")
    print("-" * 40)
    
    try:
        # Ouvrir avec PIL
        with Image.open(ico_path) as img:
            print(f"✅ Format: {img.format}")
            print(f"✅ Mode: {img.mode}")
            print(f"✅ Taille: {img.size[0]}x{img.size[1]} px")
            
            # Vérifier s'il y a plusieurs frames
            if hasattr(img, 'n_frames'):
                print(f"✅ Nombre de frames: {img.n_frames}")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur PIL: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("🎯 ANALYSEUR D'ICÔNE SOMACA")
    print("=" * 60)
    
    ico_path = "qr_icone.ico"
    
    # Analyser le fichier ICO
    success1 = analyze_ico_file(ico_path)
    
    # Tester avec PIL
    success2 = test_icon_with_pil(ico_path)
    
    print("\n" + "=" * 60)
    
    if success1 and success2:
        print("✅ Analyse terminée avec succès!")
        print("\n💡 Recommandations pour optimiser votre icône:")
        print("   1. Assurez-vous d'avoir toutes les tailles (16, 32, 48, 64, 128, 256)")
        print("   2. Utilisez des couleurs contrastées pour la lisibilité")
        print("   3. Testez la visibilité à 16x16 px (barre de titre)")
        print("   4. Gardez le design simple et reconnaissable")
    else:
        print("❌ Problèmes détectés avec l'icône!")
        print("\n🔧 Actions recommandées:")
        print("   1. Vérifiez que le fichier qr_icone.ico existe")
        print("   2. Recréez l'icône avec un outil approprié")
        print("   3. Incluez toutes les tailles recommandées")

if __name__ == "__main__":
    main()
