#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cache Manager - SOMACA

Gestionnaire de cache intelligent pour optimiser les performances.
Utilise des caches LRU pour éviter la régénération d'images identiques.
"""

import logging
import hashlib
from typing import Any, Optional, Dict
from functools import lru_cache
from ..config.constants import CACHE_SIZE, BARCODE_CACHE_SIZE, QR_CACHE_SIZE

logger = logging.getLogger(__name__)


class CacheManager:
    """
    Gestionnaire de cache centralisé pour l'application SOMACA.
    
    Utilise des caches LRU séparés pour différents types de données
    afin d'optimiser les performances et éviter les régénérations inutiles.
    """
    
    def __init__(self):
        """Initialiser le gestionnaire de cache"""
        self._barcode_cache: Dict[str, Any] = {}
        self._qr_cache: Dict[str, Any] = {}
        self._style_cache: Dict[str, Any] = {}
        self._renault_cache: Dict[str, Any] = {}
        self._image_cache: Dict[str, Any] = {}
        
        # Statistiques de cache
        self._stats = {
            'barcode_hits': 0,
            'barcode_misses': 0,
            'qr_hits': 0,
            'qr_misses': 0,
            'style_hits': 0,
            'style_misses': 0
        }
        
        logger.debug("Gestionnaire de cache initialisé")
    
    def _generate_cache_key(self, data: Any, prefix: str = "") -> str:
        """
        Générer une clé de cache unique basée sur les données.
        
        Args:
            data: Données à hasher
            prefix: Préfixe pour la clé
            
        Returns:
            Clé de cache unique
        """
        # Convertir les données en string pour le hash
        data_str = str(data)
        
        # Générer un hash MD5 pour une clé courte et unique
        hash_obj = hashlib.md5(data_str.encode('utf-8'))
        hash_key = hash_obj.hexdigest()
        
        return f"{prefix}_{hash_key}" if prefix else hash_key
    
    def get_barcode_cache(self, data: str) -> Optional[Any]:
        """
        Récupérer une image de code-barres du cache.
        
        Args:
            data: Données du code-barres
            
        Returns:
            Image mise en cache ou None
        """
        cache_key = self._generate_cache_key(data, "barcode")
        
        if cache_key in self._barcode_cache:
            self._stats['barcode_hits'] += 1
            logger.debug(f"Cache hit pour code-barres: {data[:20]}...")
            return self._barcode_cache[cache_key]
        
        self._stats['barcode_misses'] += 1
        logger.debug(f"Cache miss pour code-barres: {data[:20]}...")
        return None
    
    def set_barcode_cache(self, data: str, image: Any) -> None:
        """
        Mettre en cache une image de code-barres.
        
        Args:
            data: Données du code-barres
            image: Image à mettre en cache
        """
        cache_key = self._generate_cache_key(data, "barcode")
        
        # Gérer la taille du cache
        if len(self._barcode_cache) >= BARCODE_CACHE_SIZE:
            # Supprimer le plus ancien élément (FIFO simple)
            oldest_key = next(iter(self._barcode_cache))
            del self._barcode_cache[oldest_key]
            logger.debug("Cache code-barres plein, suppression du plus ancien")
        
        self._barcode_cache[cache_key] = image
        logger.debug(f"Image code-barres mise en cache: {data[:20]}...")
    
    def get_qr_cache(self, data: str) -> Optional[Any]:
        """
        Récupérer une image QR du cache.
        
        Args:
            data: Données du QR code
            
        Returns:
            Image mise en cache ou None
        """
        cache_key = self._generate_cache_key(data, "qr")
        
        if cache_key in self._qr_cache:
            self._stats['qr_hits'] += 1
            logger.debug(f"Cache hit pour QR: {data[:20]}...")
            return self._qr_cache[cache_key]
        
        self._stats['qr_misses'] += 1
        logger.debug(f"Cache miss pour QR: {data[:20]}...")
        return None
    
    def set_qr_cache(self, data: str, image: Any) -> None:
        """
        Mettre en cache une image QR.
        
        Args:
            data: Données du QR code
            image: Image à mettre en cache
        """
        cache_key = self._generate_cache_key(data, "qr")
        
        # Gérer la taille du cache
        if len(self._qr_cache) >= QR_CACHE_SIZE:
            # Supprimer le plus ancien élément (FIFO simple)
            oldest_key = next(iter(self._qr_cache))
            del self._qr_cache[oldest_key]
            logger.debug("Cache QR plein, suppression du plus ancien")
        
        self._qr_cache[cache_key] = image
        logger.debug(f"Image QR mise en cache: {data[:20]}...")
    
    def get_style_cache(self, style_name: str) -> Optional[Any]:
        """
        Récupérer des styles Excel du cache.
        
        Args:
            style_name: Nom du style
            
        Returns:
            Styles mis en cache ou None
        """
        if style_name in self._style_cache:
            self._stats['style_hits'] += 1
            logger.debug(f"Cache hit pour style: {style_name}")
            return self._style_cache[style_name]
        
        self._stats['style_misses'] += 1
        logger.debug(f"Cache miss pour style: {style_name}")
        return None
    
    def set_style_cache(self, style_name: str, styles: Any) -> None:
        """
        Mettre en cache des styles Excel.
        
        Args:
            style_name: Nom du style
            styles: Styles à mettre en cache
        """
        self._style_cache[style_name] = styles
        logger.debug(f"Styles mis en cache: {style_name}")
    
    def get_renault_cache(self, cache_key: str) -> Optional[Any]:
        """
        Récupérer des données Renault du cache.
        
        Args:
            cache_key: Clé de cache
            
        Returns:
            Données mises en cache ou None
        """
        return self._renault_cache.get(cache_key)
    
    def set_renault_cache(self, cache_key: str, data: Any) -> None:
        """
        Mettre en cache des données Renault.
        
        Args:
            cache_key: Clé de cache
            data: Données à mettre en cache
        """
        self._renault_cache[cache_key] = data
        logger.debug(f"Données Renault mises en cache: {cache_key}")
    
    def clear_all_caches(self) -> None:
        """Vider tous les caches"""
        self._barcode_cache.clear()
        self._qr_cache.clear()
        self._style_cache.clear()
        self._renault_cache.clear()
        self._image_cache.clear()
        
        # Réinitialiser les statistiques
        for key in self._stats:
            self._stats[key] = 0
        
        logger.info("Tous les caches ont été vidés")
    
    def clear_barcode_cache(self) -> None:
        """Vider le cache des codes-barres"""
        self._barcode_cache.clear()
        logger.info("Cache des codes-barres vidé")
    
    def clear_qr_cache(self) -> None:
        """Vider le cache des QR codes"""
        self._qr_cache.clear()
        logger.info("Cache des QR codes vidé")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Obtenir les statistiques de cache.
        
        Returns:
            Dictionnaire avec les statistiques
        """
        total_hits = self._stats['barcode_hits'] + self._stats['qr_hits'] + self._stats['style_hits']
        total_misses = self._stats['barcode_misses'] + self._stats['qr_misses'] + self._stats['style_misses']
        total_requests = total_hits + total_misses
        
        hit_rate = (total_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_sizes': {
                'barcode': len(self._barcode_cache),
                'qr': len(self._qr_cache),
                'style': len(self._style_cache),
                'renault': len(self._renault_cache),
                'image': len(self._image_cache)
            },
            'statistics': self._stats.copy(),
            'hit_rate': round(hit_rate, 2),
            'total_requests': total_requests
        }
    
    def get_cache_size_info(self) -> Dict[str, int]:
        """
        Obtenir les informations de taille des caches.
        
        Returns:
            Dictionnaire avec les tailles des caches
        """
        return {
            'barcode_cache': len(self._barcode_cache),
            'qr_cache': len(self._qr_cache),
            'style_cache': len(self._style_cache),
            'renault_cache': len(self._renault_cache),
            'image_cache': len(self._image_cache)
        }


# Instance globale du gestionnaire de cache
cache_manager = CacheManager()
