#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lazy Import Manager - SOMACA

Gestionnaire d'imports différés pour optimiser le temps de démarrage.
Les modules lourds ne sont importés qu'au moment de leur première utilisation.
"""

import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class LazyImportManager:
    """
    Gestionnaire d'imports différés pour optimiser les performances de démarrage.
    
    Utilise le pattern Singleton pour éviter les imports multiples.
    """
    
    _instance = None
    _modules: Dict[str, Any] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._modules = {}
            self._initialized = True
    
    def get_pandas(self) -> Optional[Any]:
        """Import différé de pandas"""
        if 'pandas' not in self._modules:
            try:
                import pandas as pd
                self._modules['pandas'] = pd
                logger.debug("Pandas importé avec succès")
            except ImportError as e:
                logger.error(f"Erreur import pandas: {e}")
                return None
        return self._modules['pandas']
    
    def get_qrcode(self) -> Optional[Any]:
        """Import différé de qrcode"""
        if 'qrcode' not in self._modules:
            try:
                import qrcode
                self._modules['qrcode'] = qrcode
                logger.debug("QRCode importé avec succès")
            except ImportError as e:
                logger.error(f"Erreur import qrcode: {e}")
                return None
        return self._modules['qrcode']
    
    def get_barcode(self) -> Optional[Dict[str, Any]]:
        """Import différé de barcode avec ImageWriter"""
        if 'barcode' not in self._modules:
            try:
                import barcode
                from barcode.writer import ImageWriter
                self._modules['barcode'] = {
                    'module': barcode,
                    'ImageWriter': ImageWriter
                }
                logger.debug("Barcode importé avec succès")
            except ImportError as e:
                logger.error(f"Erreur import barcode: {e}")
                return None
        return self._modules['barcode']
    
    def get_openpyxl(self) -> Optional[Dict[str, Any]]:
        """Import différé d'openpyxl avec tous les composants nécessaires"""
        if 'openpyxl' not in self._modules:
            try:
                from openpyxl import Workbook, load_workbook
                from openpyxl.drawing.image import Image as OpenpyxlImage
                from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
                
                self._modules['openpyxl'] = {
                    'Workbook': Workbook,
                    'load_workbook': load_workbook,
                    'Image': OpenpyxlImage,
                    'Font': Font,
                    'PatternFill': PatternFill,
                    'Alignment': Alignment,
                    'Border': Border,
                    'Side': Side
                }
                logger.debug("OpenPyXL importé avec succès")
            except ImportError as e:
                logger.error(f"Erreur import openpyxl: {e}")
                return None
        return self._modules['openpyxl']
    
    def get_pil(self) -> Optional[Any]:
        """Import différé de PIL/Pillow"""
        if 'pil' not in self._modules:
            try:
                from PIL import Image, ImageDraw, ImageFont
                self._modules['pil'] = {
                    'Image': Image,
                    'ImageDraw': ImageDraw,
                    'ImageFont': ImageFont
                }
                logger.debug("PIL importé avec succès")
            except ImportError as e:
                logger.error(f"Erreur import PIL: {e}")
                return None
        return self._modules['pil']
    
    def get_tkinter(self) -> Optional[Dict[str, Any]]:
        """Import différé de tkinter"""
        if 'tkinter' not in self._modules:
            try:
                import tkinter as tk
                from tkinter import filedialog, messagebox
                self._modules['tkinter'] = {
                    'tk': tk,
                    'filedialog': filedialog,
                    'messagebox': messagebox
                }
                logger.debug("Tkinter importé avec succès")
            except ImportError as e:
                logger.error(f"Erreur import tkinter: {e}")
                return None
        return self._modules['tkinter']
    
    def get_win32(self) -> Optional[Dict[str, Any]]:
        """Import différé de win32 (Windows uniquement)"""
        if 'win32' not in self._modules:
            try:
                import win32print
                import win32api
                self._modules['win32'] = {
                    'print': win32print,
                    'api': win32api
                }
                logger.debug("Win32 importé avec succès")
            except ImportError as e:
                logger.warning(f"Win32 non disponible (normal sur non-Windows): {e}")
                return None
        return self._modules['win32']
    
    def clear_cache(self):
        """Vider le cache des modules importés"""
        self._modules.clear()
        logger.info("Cache des imports différés vidé")
    
    def get_loaded_modules(self) -> list:
        """Retourner la liste des modules actuellement chargés"""
        return list(self._modules.keys())


# Instance globale du gestionnaire d'imports
lazy_imports = LazyImportManager()
