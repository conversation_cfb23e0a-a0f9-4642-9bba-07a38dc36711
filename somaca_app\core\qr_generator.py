#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Code Generator - SOMACA

Générateur de QR codes optimisé avec cache intelligent.
Spécialisé dans la génération de QR codes pour Excel et étiquettes.
"""

import io
import logging
from typing import Optional, Any, Dict

from ..config.settings import config
from ..utils.lazy_imports import lazy_imports
from ..utils.text_utils import TextCleaner
from ..utils.cache_manager import cache_manager
from ..utils.logger import get_logger

logger = get_logger(__name__)


class QRGenerator:
    """
    Générateur de QR codes optimisé.
    
    Utilise un cache intelligent et supporte différents formats
    de données pour une compatibilité maximale iOS/Android.
    """
    
    def __init__(self):
        self._qr_config = None  # Cache de la configuration QR
        logger.debug("Générateur de QR codes initialisé")
    
    def generate_qr_image(self, data: str) -> Optional[Any]:
        """
        Générer une image QR code pour Excel.
        
        Args:
            data: Données à encoder
            
        Returns:
            Image OpenPyXL ou None si erreur
        """
        try:
            # Vérifier le cache d'abord
            cached_image = cache_manager.get_qr_cache(data)
            if cached_image is not None:
                logger.debug(f"Image QR récupérée du cache: {data[:20]}...")
                return cached_image
            
            # Import différé du module qrcode
            qrcode_module = lazy_imports.get_qrcode()
            if not qrcode_module:
                logger.error("Module qrcode non disponible")
                return None
            
            # Configuration optimisée avec cache
            if self._qr_config is None:
                error_correction_map = {
                    'L': qrcode_module.constants.ERROR_CORRECT_L,
                    'M': qrcode_module.constants.ERROR_CORRECT_M,
                    'Q': qrcode_module.constants.ERROR_CORRECT_Q,
                    'H': qrcode_module.constants.ERROR_CORRECT_H
                }
                
                self._qr_config = {
                    'version': None,  # Auto-détection
                    'error_correction': error_correction_map.get(
                        config.image.qr_error_correction, 
                        qrcode_module.constants.ERROR_CORRECT_M
                    ),
                    'box_size': config.image.qr_box_size,
                    'border': config.image.qr_border
                }
                logger.debug("Configuration QR mise en cache")
            
            # Créer le QR code
            qr = qrcode_module.QRCode(**self._qr_config)
            
            # Nettoyer et formater les données
            clean_data = TextCleaner.clean_for_qr(data)
            qr.add_data(clean_data)
            qr.make(fit=True)
            
            # Générer l'image
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Convertir en buffer
            buffer = io.BytesIO()
            qr_img.save(buffer, format='PNG')
            buffer.seek(0)
            
            logger.debug(f"QR code généré: {clean_data[:50]}...")
            
            # Convertir en image OpenPyXL
            openpyxl_module = lazy_imports.get_openpyxl()
            if not openpyxl_module:
                logger.error("Module OpenPyXL non disponible")
                return None
            
            try:
                img = openpyxl_module['Image'](buffer)
                
                # Définir les dimensions selon la configuration
                img.width = config.image.qr_size
                img.height = config.image.qr_size
                
                # Mettre en cache pour réutilisation
                cache_manager.set_qr_cache(data, img)
                
                logger.debug(f"Image QR créée: {img.width}x{img.height}px")
                return img
                
            except Exception as e:
                logger.error(f"Erreur création image OpenPyXL: {e}")
                return None
        
        except Exception as e:
            logger.error(f"Erreur génération QR code: {e}")
            return None
    
    def generate_qr_base64(self, data: str, size_cm: float = 2.0) -> Optional[str]:
        """
        Générer un QR code en base64 pour les étiquettes Renault.
        
        Args:
            data: Données à encoder
            size_cm: Taille en centimètres (carré)
            
        Returns:
            String base64 de l'image ou None si erreur
        """
        try:
            # Import différé du module qrcode
            qrcode_module = lazy_imports.get_qrcode()
            if not qrcode_module:
                logger.error("Module qrcode non disponible")
                return None
            
            # Nettoyer les données
            clean_data = TextCleaner.clean_for_qr(data)
            
            # Créer le QR code avec configuration optimisée
            qr = qrcode_module.QRCode(
                version=1,
                error_correction=qrcode_module.constants.ERROR_CORRECT_L,
                box_size=10,
                border=0  # Sans bordure
            )
            qr.add_data(clean_data)
            qr.make(fit=True)
            
            # Créer l'image QR
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Convertir en base64
            from ..utils.image_utils import ImageProcessor
            base64_string = ImageProcessor.pil_to_base64(qr_img, "PNG")
            
            logger.debug(f"QR code base64 généré: {size_cm}x{size_cm}cm")
            return base64_string
            
        except Exception as e:
            logger.error(f"Erreur génération QR base64: {e}")
            return None
    
    def generate_qr_multiline(self, row_data: list, headers: list = None) -> Optional[Any]:
        """
        Générer un QR code avec données multi-lignes format iOS/Android.
        
        Args:
            row_data: Données de la ligne
            headers: En-têtes des colonnes (optionnel)
            
        Returns:
            Image OpenPyXL ou None si erreur
        """
        try:
            # Créer les données formatées
            qr_data = TextCleaner.create_qr_data_from_row(row_data, headers)
            
            if not qr_data:
                logger.warning("Aucune donnée valide pour QR multi-lignes")
                return None
            
            # Utiliser le format multi-lignes
            formatted_data = TextCleaner.format_qr_data_multiline(qr_data)
            
            # Générer l'image QR
            return self.generate_qr_image(formatted_data)
            
        except Exception as e:
            logger.error(f"Erreur génération QR multi-lignes: {e}")
            return None
    
    def create_vcard_qr(self, contact_info: Dict[str, str]) -> Optional[Any]:
        """
        Créer un QR code vCard pour contact.
        
        Args:
            contact_info: Informations de contact
                - name: Nom
                - phone: Téléphone
                - email: Email
                - company: Entreprise
                
        Returns:
            Image OpenPyXL ou None si erreur
        """
        try:
            # Construire le format vCard
            vcard_lines = ["BEGIN:VCARD", "VERSION:3.0"]
            
            if contact_info.get('name'):
                vcard_lines.append(f"FN:{contact_info['name']}")
            
            if contact_info.get('phone'):
                vcard_lines.append(f"TEL:{contact_info['phone']}")
            
            if contact_info.get('email'):
                vcard_lines.append(f"EMAIL:{contact_info['email']}")
            
            if contact_info.get('company'):
                vcard_lines.append(f"ORG:{contact_info['company']}")
            
            vcard_lines.append("END:VCARD")
            
            vcard_data = "\n".join(vcard_lines)
            
            # Générer le QR code
            return self.generate_qr_image(vcard_data)
            
        except Exception as e:
            logger.error(f"Erreur génération QR vCard: {e}")
            return None
    
    def create_wifi_qr(self, ssid: str, password: str, security: str = "WPA") -> Optional[Any]:
        """
        Créer un QR code WiFi.
        
        Args:
            ssid: Nom du réseau
            password: Mot de passe
            security: Type de sécurité (WPA, WEP, nopass)
            
        Returns:
            Image OpenPyXL ou None si erreur
        """
        try:
            # Format WiFi QR: WIFI:T:WPA;S:mynetwork;P:mypass;;
            wifi_data = f"WIFI:T:{security};S:{ssid};P:{password};;"
            
            # Générer le QR code
            return self.generate_qr_image(wifi_data)
            
        except Exception as e:
            logger.error(f"Erreur génération QR WiFi: {e}")
            return None
    
    def validate_qr_data(self, data: str) -> tuple[bool, str]:
        """
        Valider les données pour un QR code.
        
        Args:
            data: Données à valider
            
        Returns:
            Tuple (is_valid, message)
        """
        if not data:
            return False, "Données vides"
        
        # Les QR codes supportent beaucoup plus de données que les codes-barres
        if len(data) > 2000:  # Limite raisonnable
            return False, "Données trop longues (max 2000 caractères)"
        
        try:
            # Tester l'encodage UTF-8
            data.encode('utf-8')
            return True, "Données valides"
        except UnicodeEncodeError:
            return False, "Caractères non supportés"
    
    def clear_cache(self):
        """Vider le cache des QR codes"""
        cache_manager.clear_qr_cache()
        self._qr_config = None
        logger.info("Cache QR codes vidé")
    
    def get_supported_formats(self) -> list:
        """
        Obtenir les formats supportés.
        
        Returns:
            Liste des formats supportés
        """
        return [
            'Text', 'URL', 'Email', 'Phone', 'SMS', 
            'WiFi', 'vCard', 'Multi-line'
        ]
