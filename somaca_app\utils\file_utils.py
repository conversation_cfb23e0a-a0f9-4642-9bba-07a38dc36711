#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File Utilities - SOMACA

Utilitaires pour la gestion des fichiers et dossiers.
Inclut la recherche de fichiers, la gestion des conflits et la validation.
"""

import os
import tempfile
import shutil
import logging
from typing import Optional, List, Tuple
from pathlib import Path
from ..config.constants import SUPPORTED_EXCEL_FORMATS, DEFAULT_OUTPUT_SUFFIX

logger = logging.getLogger(__name__)


class FileManager:
    """
    Gestionnaire de fichiers pour l'application SOMACA.
    
    Fournit des méthodes pour la recherche, validation et gestion des fichiers.
    """
    
    @staticmethod
    def find_file(filename: str, search_paths: List[str] = None) -> Optional[str]:
        """
        Rechercher un fichier dans plusieurs emplacements.
        
        Args:
            filename: Nom du fichier à rechercher
            search_paths: Liste des chemins de recherche (optionnel)
            
        Returns:
            Chemin complet du fichier trouvé ou None
        """
        # Si c'est déjà un chemin absolu et qu'il existe
        if os.path.isabs(filename) and os.path.exists(filename):
            logger.debug(f"Fichier trouvé (chemin absolu): {filename}")
            return filename
        
        # Si c'est un chemin relatif qui existe
        if os.path.exists(filename):
            abs_path = os.path.abspath(filename)
            logger.debug(f"Fichier trouvé (chemin relatif): {abs_path}")
            return abs_path
        
        # Chemins de recherche par défaut
        if search_paths is None:
            search_paths = [
                os.getcwd(),
                os.path.dirname(os.path.abspath(__file__)),
                os.path.expanduser("~"),
                os.path.expanduser("~/Desktop"),
                os.path.expanduser("~/Documents"),
                os.path.expanduser("~/Downloads")
            ]
        
        # Rechercher dans tous les chemins
        for search_path in search_paths:
            full_path = os.path.join(search_path, filename)
            if os.path.exists(full_path):
                logger.debug(f"Fichier trouvé dans {search_path}: {full_path}")
                return full_path
        
        logger.warning(f"Fichier non trouvé: {filename}")
        return None
    
    @staticmethod
    def validate_excel_file(file_path: str) -> Tuple[bool, str]:
        """
        Valider qu'un fichier Excel est lisible.
        
        Args:
            file_path: Chemin du fichier Excel
            
        Returns:
            Tuple (is_valid, message)
        """
        if not file_path:
            return False, "Chemin de fichier vide"
        
        if not os.path.exists(file_path):
            return False, f"Fichier non trouvé: {file_path}"
        
        # Vérifier l'extension
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in SUPPORTED_EXCEL_FORMATS:
            return False, f"Format non supporté: {file_ext}. Formats acceptés: {', '.join(SUPPORTED_EXCEL_FORMATS)}"
        
        # Vérifier que le fichier n'est pas vide
        try:
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False, "Le fichier est vide"
        except OSError as e:
            return False, f"Erreur d'accès au fichier: {e}"
        
        # Tenter d'ouvrir le fichier pour validation basique
        try:
            with open(file_path, 'rb') as f:
                # Lire les premiers bytes pour vérifier que c'est lisible
                f.read(1024)
        except Exception as e:
            return False, f"Fichier illisible: {e}"
        
        logger.debug(f"Fichier Excel validé: {file_path}")
        return True, "Fichier valide"
    
    @staticmethod
    def ensure_directory(directory_path: str) -> Tuple[bool, str]:
        """
        S'assurer qu'un dossier existe, le créer si nécessaire.
        
        Args:
            directory_path: Chemin du dossier
            
        Returns:
            Tuple (success, message)
        """
        if not directory_path:
            return False, "Chemin de dossier vide"
        
        try:
            if not os.path.exists(directory_path):
                os.makedirs(directory_path, exist_ok=True)
                logger.info(f"Dossier créé: {directory_path}")
            
            # Vérifier que c'est bien un dossier et qu'on peut y écrire
            if not os.path.isdir(directory_path):
                return False, f"Le chemin existe mais n'est pas un dossier: {directory_path}"
            
            if not os.access(directory_path, os.W_OK):
                return False, f"Pas de permission d'écriture dans: {directory_path}"
            
            return True, f"Dossier prêt: {directory_path}"
            
        except Exception as e:
            logger.error(f"Erreur création dossier {directory_path}: {e}")
            return False, f"Erreur création dossier: {e}"
    
    @staticmethod
    def generate_output_filename(input_file: str, suffix: str = None, extension: str = None) -> str:
        """
        Générer un nom de fichier de sortie basé sur le fichier d'entrée.
        
        Args:
            input_file: Fichier d'entrée
            suffix: Suffixe à ajouter (par défaut DEFAULT_OUTPUT_SUFFIX)
            extension: Extension du fichier de sortie (par défaut .xlsx)
            
        Returns:
            Nom du fichier de sortie
        """
        if suffix is None:
            suffix = DEFAULT_OUTPUT_SUFFIX
        if extension is None:
            extension = ".xlsx"
        
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_filename = f"{base_name}{suffix}{extension}"
        
        logger.debug(f"Nom de fichier généré: {output_filename}")
        return output_filename
    
    @staticmethod
    def handle_file_conflict(file_path: str) -> Tuple[str, bool]:
        """
        Gérer les conflits de noms de fichiers.
        
        Args:
            file_path: Chemin du fichier
            
        Returns:
            Tuple (nouveau_chemin, fichier_existait)
        """
        if not os.path.exists(file_path):
            return file_path, False
        
        # Le fichier existe, générer un nom unique
        directory = os.path.dirname(file_path)
        filename = os.path.basename(file_path)
        name, ext = os.path.splitext(filename)
        
        counter = 1
        while True:
            new_filename = f"{name}_{counter}{ext}"
            new_path = os.path.join(directory, new_filename)
            if not os.path.exists(new_path):
                logger.info(f"Conflit résolu: {file_path} -> {new_path}")
                return new_path, True
            counter += 1
    
    @staticmethod
    def get_temp_file(suffix: str = ".tmp", prefix: str = "somaca_") -> str:
        """
        Créer un fichier temporaire.
        
        Args:
            suffix: Suffixe du fichier
            prefix: Préfixe du fichier
            
        Returns:
            Chemin du fichier temporaire
        """
        fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
        os.close(fd)  # Fermer le descripteur de fichier
        logger.debug(f"Fichier temporaire créé: {temp_path}")
        return temp_path
    
    @staticmethod
    def cleanup_temp_files(temp_files: List[str]):
        """
        Nettoyer une liste de fichiers temporaires.
        
        Args:
            temp_files: Liste des fichiers temporaires à supprimer
        """
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    logger.debug(f"Fichier temporaire supprimé: {temp_file}")
            except Exception as e:
                logger.warning(f"Erreur suppression fichier temporaire {temp_file}: {e}")
    
    @staticmethod
    def get_file_info(file_path: str) -> dict:
        """
        Obtenir des informations sur un fichier.
        
        Args:
            file_path: Chemin du fichier
            
        Returns:
            Dictionnaire avec les informations du fichier
        """
        if not os.path.exists(file_path):
            return {"exists": False}
        
        try:
            stat = os.stat(file_path)
            return {
                "exists": True,
                "size": stat.st_size,
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "modified": stat.st_mtime,
                "is_file": os.path.isfile(file_path),
                "is_dir": os.path.isdir(file_path),
                "readable": os.access(file_path, os.R_OK),
                "writable": os.access(file_path, os.W_OK),
                "extension": os.path.splitext(file_path)[1].lower()
            }
        except Exception as e:
            logger.error(f"Erreur obtention info fichier {file_path}: {e}")
            return {"exists": True, "error": str(e)}
    
    @staticmethod
    def safe_copy_file(src: str, dst: str) -> Tuple[bool, str]:
        """
        Copier un fichier de manière sécurisée.
        
        Args:
            src: Fichier source
            dst: Fichier destination
            
        Returns:
            Tuple (success, message)
        """
        try:
            # S'assurer que le dossier de destination existe
            dst_dir = os.path.dirname(dst)
            if dst_dir:
                success, msg = FileManager.ensure_directory(dst_dir)
                if not success:
                    return False, f"Erreur création dossier destination: {msg}"
            
            # Copier le fichier
            shutil.copy2(src, dst)
            logger.info(f"Fichier copié: {src} -> {dst}")
            return True, f"Fichier copié avec succès vers {dst}"
            
        except Exception as e:
            logger.error(f"Erreur copie fichier {src} -> {dst}: {e}")
            return False, f"Erreur copie: {e}"
