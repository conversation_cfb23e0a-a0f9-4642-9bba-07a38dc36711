#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Barcode Generator - SOMACA

Générateur de codes-barres optimisé avec cache intelligent.
Spécialisé dans la génération de codes-barres Code128 pour Excel.
"""

import io
import logging
from typing import Optional, Any
from functools import lru_cache

from ..config.settings import config
from ..utils.lazy_imports import lazy_imports
from ..utils.text_utils import TextCleaner
from ..utils.cache_manager import cache_manager
from ..utils.logger import get_logger

logger = get_logger(__name__)


class BarcodeGenerator:
    """
    Générateur de codes-barres Code128 optimisé.
    
    Utilise un cache intelligent pour éviter la régénération
    d'images identiques et optimise les performances.
    """
    
    def __init__(self):
        self._code128_class = None  # Cache de la classe Code128
        logger.debug("Générateur de codes-barres initialisé")
    
    def generate_barcode_image(self, data: str) -> Optional[Any]:
        """
        Générer une image de code-barres Code128 pour Excel.
        
        Args:
            data: Données à encoder
            
        Returns:
            Image OpenPyXL ou None si erreur
        """
        try:
            # Vérifier le cache d'abord
            cached_image = cache_manager.get_barcode_cache(data)
            if cached_image is not None:
                logger.debug(f"Image code-barres récupérée du cache: {data[:20]}...")
                return cached_image
            
            # Nettoyer les données pour Code128
            cleaned_data = TextCleaner.clean_for_barcode(data)
            
            # Vérifications de base
            if not cleaned_data or not cleaned_data.strip():
                logger.warning(f"Données vides après nettoyage: '{data}'")
                return None
            
            if not TextCleaner.is_ascii_compatible(cleaned_data):
                logger.warning(f"Données non compatibles ASCII: '{data}'")
                return None
            
            # Import différé du module barcode
            barcode_module = lazy_imports.get_barcode()
            if not barcode_module:
                logger.error("Module barcode non disponible")
                return None
            
            # Cache de la classe Code128 pour optimisation
            if self._code128_class is None:
                try:
                    self._code128_class = barcode_module['module'].get_barcode_class('code128')
                    logger.debug("Classe Code128 mise en cache")
                except Exception as e:
                    logger.error(f"Erreur obtention classe Code128: {e}")
                    return None
            
            # Générer le code-barres
            try:
                barcode_instance = self._code128_class(
                    cleaned_data, 
                    writer=barcode_module['ImageWriter']()
                )
                
                # Configurer les options du writer
                barcode_instance.writer.set_options({
                    'module_width': config.image.barcode_module_width,
                    'module_height': config.image.barcode_module_height,
                    'quiet_zone': config.image.barcode_quiet_zone,
                    'dpi': config.image.barcode_dpi,
                    'background': 'white',
                    'foreground': 'black'
                })
                
                # Générer l'image en mémoire
                buffer = io.BytesIO()
                barcode_instance.write(buffer)
                buffer.seek(0)
                
                logger.debug(f"Code-barres généré: {cleaned_data}")
                
            except Exception as e:
                logger.error(f"Erreur génération code-barres '{cleaned_data}': {e}")
                return None
            
            # Convertir en image OpenPyXL
            openpyxl_module = lazy_imports.get_openpyxl()
            if not openpyxl_module:
                logger.error("Module OpenPyXL non disponible")
                return None
            
            try:
                img = openpyxl_module['Image'](buffer)
                
                # Définir les dimensions selon la configuration
                img.width = config.image.barcode_width
                img.height = config.image.barcode_height
                
                # Mettre en cache pour réutilisation
                cache_manager.set_barcode_cache(data, img)
                
                logger.debug(f"Image code-barres créée: {img.width}x{img.height}px")
                return img
                
            except Exception as e:
                logger.error(f"Erreur création image OpenPyXL: {e}")
                return None
        
        except Exception as e:
            logger.error(f"Erreur générale génération code-barres: {e}")
            return None
    
    def generate_barcode_base64(self, data: str, width_cm: float = 4.5, 
                               height_cm: float = 3.0) -> Optional[str]:
        """
        Générer un code-barres en base64 pour les étiquettes Renault.
        
        Args:
            data: Données à encoder
            width_cm: Largeur en centimètres
            height_cm: Hauteur en centimètres
            
        Returns:
            String base64 de l'image ou None si erreur
        """
        try:
            # Nettoyer les données
            cleaned_data = TextCleaner.clean_for_barcode(data)
            if not cleaned_data:
                logger.warning(f"Données vides après nettoyage: '{data}'")
                return None
            
            # Import différé des modules
            barcode_module = lazy_imports.get_barcode()
            pil_module = lazy_imports.get_pil()
            
            if not barcode_module or not pil_module:
                logger.error("Modules barcode ou PIL non disponibles")
                return None
            
            # Calculer les dimensions en pixels
            from ..utils.image_utils import ImageProcessor
            target_width_px = ImageProcessor.cm_to_pixels(width_cm, config.image.default_dpi)
            target_height_px = ImageProcessor.cm_to_pixels(height_cm, config.image.default_dpi)
            
            # Générer le code-barres
            code128 = barcode_module['module'].get_barcode_class('code128')
            writer = barcode_module['ImageWriter']()
            writer.set_options({
                'module_width': 0.6,
                'module_height': 35,
                'quiet_zone': 1,
                'dpi': config.image.default_dpi,
                'background': 'white',
                'foreground': 'black'
            })
            
            barcode_instance = code128(cleaned_data, writer=writer)
            
            # Générer l'image
            buffer = io.BytesIO()
            barcode_instance.write(buffer)
            buffer.seek(0)
            
            # Ouvrir avec PIL pour traitement
            barcode_img = pil_module['Image'].open(buffer)
            width, height = barcode_img.size
            
            # Couper la partie texte (garder seulement les barres)
            cut_height = int(height * 0.7)
            barcode_only_bars = barcode_img.crop((0, 0, width, cut_height))
            
            # Redimensionner aux dimensions exactes
            try:
                barcode_final = barcode_only_bars.resize(
                    (target_width_px, target_height_px), 
                    pil_module['Image'].Resampling.LANCZOS
                )
            except AttributeError:
                # Fallback pour anciennes versions PIL
                barcode_final = barcode_only_bars.resize(
                    (target_width_px, target_height_px), 
                    pil_module['Image'].LANCZOS
                )
            
            # Convertir en base64
            from ..utils.image_utils import ImageProcessor
            base64_string = ImageProcessor.pil_to_base64(barcode_final, "PNG")
            
            logger.debug(f"Code-barres base64 généré: {width_cm}x{height_cm}cm")
            return base64_string
            
        except Exception as e:
            logger.error(f"Erreur génération code-barres base64: {e}")
            return None
    
    def validate_barcode_data(self, data: str) -> tuple[bool, str]:
        """
        Valider les données pour un code-barres.
        
        Args:
            data: Données à valider
            
        Returns:
            Tuple (is_valid, message)
        """
        if not data:
            return False, "Données vides"
        
        # Nettoyer les données
        cleaned_data = TextCleaner.clean_for_barcode(data)
        
        if not cleaned_data:
            return False, "Données vides après nettoyage"
        
        if not TextCleaner.is_ascii_compatible(cleaned_data):
            return False, "Caractères non compatibles Code128"
        
        if len(cleaned_data) > 100:  # Limite raisonnable
            return False, "Données trop longues (max 100 caractères)"
        
        return True, "Données valides"
    
    def clear_cache(self):
        """Vider le cache des codes-barres"""
        cache_manager.clear_barcode_cache()
        self._code128_class = None
        logger.info("Cache codes-barres vidé")
    
    def get_supported_formats(self) -> list:
        """
        Obtenir les formats supportés.
        
        Returns:
            Liste des formats supportés
        """
        return ['Code128']  # Pour l'instant seulement Code128
