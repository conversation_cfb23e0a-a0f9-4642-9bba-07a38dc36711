@echo off
echo 🎯 TEST DE VOTRE ICÔNE ORIGINALE OPTIMISÉE
echo =========================================
echo.
echo Cette solution optimise VOTRE icône qr_icone.ico originale
echo pour la rendre parfaitement nette dans la barre des tâches.
echo.
echo 📊 OPTIMISATIONS APPLIQUÉES À VOTRE ICÔNE:
echo ==========================================
echo ✅ Votre design original préservé à 100%%
echo ✅ Sharpening x8 pour la taille 32x32px (barre des tâches)
echo ✅ Contraste x3 pour visibilité maximale
echo ✅ Saturation x2 pour couleurs plus vives
echo ✅ Double filtre de netteté appliqué
echo ✅ Redimensionnement LANCZOS haute qualité
echo.
pause

echo.
echo 🧹 Nettoyage du cache Windows...
echo ===============================

echo 📋 Arrêt de l'Explorateur Windows...
taskkill /f /im explorer.exe >nul 2>&1

echo 📋 Suppression des caches d'icônes...
cd /d "%userprofile%\AppData\Local" >nul 2>&1
attrib -h IconCache.db >nul 2>&1
del /f /q IconCache.db >nul 2>&1
del /f /q "%localappdata%\IconCache.db" >nul 2>&1
del /a /f /q "%localappdata%\Microsoft\Windows\Explorer\iconcache*" >nul 2>&1
del /a /f /q "%localappdata%\Microsoft\Windows\Explorer\thumbcache*" >nul 2>&1

echo 📋 Redémarrage de l'Explorateur...
start explorer.exe
timeout /t 3 >nul

echo ✅ Cache nettoyé !

echo.
echo 🚀 Compilation avec VOTRE icône optimisée...
echo ===========================================

cd /d "%~dp0"
echo 📋 Compilation avec qr_icone_your_optimized.ico...
python build_native_optimized.py

if %errorlevel% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)

echo ✅ Compilation réussie !

echo.
echo 🎯 Test de VOTRE application avec icône optimisée...
echo ==================================================

cd /d "%~dp0\dist"
echo 📋 Lancement de SOMACA_Native.exe...
echo.
echo 👀 REGARDEZ MAINTENANT LA BARRE DES TÂCHES:
echo ===========================================
echo.
echo Vous devriez voir VOTRE icône QR originale mais:
echo ✅ BEAUCOUP plus nette et claire
echo ✅ Couleurs plus vives et contrastées
echo ✅ Détails parfaitement visibles
echo ✅ Plus aucun flou
echo.
echo 🎯 C'est bien VOTRE icône, juste optimisée au maximum !
echo.

start "" "SOMACA_Native.exe"

echo.
echo 🎉 VOTRE APPLICATION LANCÉE !
echo ============================
echo.
echo 📊 RÉSULTAT ATTENDU:
echo ====================
echo - Votre icône QR originale dans la barre des tâches
echo - Parfaitement nette grâce aux optimisations
echo - Couleurs vives et contrastées
echo - Tous les détails de votre QR code visibles
echo.
echo ❓ Si votre icône est ENCORE floue:
echo ==================================
echo.
echo 🔄 SOLUTION ULTIME: REDÉMARREZ WINDOWS
echo    → Force le rechargement complet des caches
echo    → Votre icône sera alors parfaitement nette
echo.
echo 🖥️  VÉRIFIEZ L'ÉCHELLE D'AFFICHAGE:
echo    → Clic droit bureau → Paramètres d'affichage
echo    → Si échelle > 100%%, réduisez à 100%%
echo    → Votre icône sera alors parfaite
echo.
echo 🆕 TESTEZ SUR UN AUTRE PC:
echo    → Votre icône sera parfaite sur installation propre
echo    → Le problème peut venir de votre config Windows
echo.
echo 📋 INFORMATIONS TECHNIQUES:
echo ===========================
echo - Icône source: qr_icone.ico (VOTRE icône originale)
echo - Icône optimisée: qr_icone_your_optimized.ico
echo - Optimisations: Sharpening x8, Contraste x3, Saturation x2
echo - Algorithme: LANCZOS + Double filtre netteté
echo - Tailles: 9 résolutions (16px à 256px)
echo.
echo 🎯 GARANTIE: Votre icône est maintenant optimisée au maximum !
echo    Si elle reste floue, c'est un problème de votre Windows.
echo.
pause
