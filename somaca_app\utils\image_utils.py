#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Image Utilities - SOMACA

Utilitaires pour le traitement et la manipulation d'images.
Inclut la conversion, le redimensionnement et l'optimisation.
"""

import io
import base64
import logging
from typing import Op<PERSON>, <PERSON><PERSON>, Any
from ..config.constants import DEFAULT_DPI
from .lazy_imports import lazy_imports

logger = logging.getLogger(__name__)


class ImageProcessor:
    """
    Processeur d'images pour l'application SOMACA.
    
    Fournit des méthodes pour la conversion, le redimensionnement
    et l'optimisation des images pour les codes-barres et QR codes.
    """
    
    @staticmethod
    def convert_to_base64(image_path: str) -> Optional[str]:
        """
        Convertir une image en base64.
        
        Args:
            image_path: Chemin vers l'image
            
        Returns:
            String base64 de l'image ou None si erreur
        """
        try:
            with open(image_path, "rb") as image_file:
                image_data = image_file.read()
                base64_string = base64.b64encode(image_data).decode('utf-8')
                logger.debug(f"Image convertie en base64: {image_path}")
                return base64_string
        except Exception as e:
            logger.error(f"Erreur conversion base64 {image_path}: {e}")
            return None
    
    @staticmethod
    def pil_to_base64(pil_image: Any, format: str = "PNG") -> Optional[str]:
        """
        Convertir une image PIL en base64.
        
        Args:
            pil_image: Image PIL
            format: Format de sortie (PNG, JPEG, etc.)
            
        Returns:
            String base64 de l'image ou None si erreur
        """
        try:
            buffer = io.BytesIO()
            pil_image.save(buffer, format=format)
            buffer.seek(0)
            
            base64_string = base64.b64encode(buffer.getvalue()).decode('utf-8')
            logger.debug(f"Image PIL convertie en base64 (format: {format})")
            return base64_string
        except Exception as e:
            logger.error(f"Erreur conversion PIL vers base64: {e}")
            return None
    
    @staticmethod
    def resize_image(image_path: str, target_size: Tuple[int, int], 
                    output_path: str = None, maintain_aspect: bool = True) -> Optional[str]:
        """
        Redimensionner une image.
        
        Args:
            image_path: Chemin de l'image source
            target_size: Taille cible (largeur, hauteur)
            output_path: Chemin de sortie (optionnel)
            maintain_aspect: Maintenir les proportions
            
        Returns:
            Chemin de l'image redimensionnée ou None si erreur
        """
        try:
            pil_module = lazy_imports.get_pil()
            if not pil_module:
                logger.error("PIL non disponible pour redimensionnement")
                return None
            
            # Ouvrir l'image
            with pil_module['Image'].open(image_path) as img:
                if maintain_aspect:
                    # Redimensionner en maintenant les proportions
                    img.thumbnail(target_size, pil_module['Image'].Resampling.LANCZOS)
                else:
                    # Redimensionner exactement à la taille cible
                    img = img.resize(target_size, pil_module['Image'].Resampling.LANCZOS)
                
                # Déterminer le chemin de sortie
                if output_path is None:
                    base_name = os.path.splitext(image_path)[0]
                    output_path = f"{base_name}_resized.png"
                
                # Sauvegarder l'image redimensionnée
                img.save(output_path, "PNG")
                logger.debug(f"Image redimensionnée: {image_path} -> {output_path}")
                return output_path
                
        except Exception as e:
            logger.error(f"Erreur redimensionnement {image_path}: {e}")
            return None
    
    @staticmethod
    def create_openpyxl_image(image_data: bytes, width_px: int = None, 
                             height_px: int = None) -> Optional[Any]:
        """
        Créer une image OpenPyXL à partir de données binaires.
        
        Args:
            image_data: Données binaires de l'image
            width_px: Largeur en pixels (optionnel)
            height_px: Hauteur en pixels (optionnel)
            
        Returns:
            Objet Image OpenPyXL ou None si erreur
        """
        try:
            openpyxl_module = lazy_imports.get_openpyxl()
            if not openpyxl_module:
                logger.error("OpenPyXL non disponible")
                return None
            
            # Créer l'image à partir des données
            buffer = io.BytesIO(image_data)
            img = openpyxl_module['Image'](buffer)
            
            # Définir les dimensions si spécifiées
            if width_px is not None:
                img.width = width_px
            if height_px is not None:
                img.height = height_px
            
            logger.debug(f"Image OpenPyXL créée ({img.width}x{img.height})")
            return img
            
        except Exception as e:
            logger.error(f"Erreur création image OpenPyXL: {e}")
            return None
    
    @staticmethod
    def optimize_image_for_excel(pil_image: Any, max_width: int = 500, 
                                max_height: int = 300) -> Optional[Any]:
        """
        Optimiser une image PIL pour Excel.
        
        Args:
            pil_image: Image PIL source
            max_width: Largeur maximale
            max_height: Hauteur maximale
            
        Returns:
            Image PIL optimisée ou None si erreur
        """
        try:
            # Redimensionner si nécessaire
            width, height = pil_image.size
            if width > max_width or height > max_height:
                # Calculer le ratio pour maintenir les proportions
                ratio = min(max_width / width, max_height / height)
                new_width = int(width * ratio)
                new_height = int(height * ratio)
                
                pil_module = lazy_imports.get_pil()
                if pil_module:
                    pil_image = pil_image.resize((new_width, new_height), 
                                               pil_module['Image'].Resampling.LANCZOS)
                    logger.debug(f"Image optimisée pour Excel: {width}x{height} -> {new_width}x{new_height}")
            
            return pil_image
            
        except Exception as e:
            logger.error(f"Erreur optimisation image pour Excel: {e}")
            return None
    
    @staticmethod
    def cm_to_pixels(cm: float, dpi: int = DEFAULT_DPI) -> int:
        """
        Convertir des centimètres en pixels.
        
        Args:
            cm: Valeur en centimètres
            dpi: DPI (dots per inch)
            
        Returns:
            Valeur en pixels
        """
        # 1 inch = 2.54 cm
        inches = cm / 2.54
        pixels = int(inches * dpi)
        logger.debug(f"Conversion: {cm}cm = {pixels}px à {dpi} DPI")
        return pixels
    
    @staticmethod
    def pixels_to_cm(pixels: int, dpi: int = DEFAULT_DPI) -> float:
        """
        Convertir des pixels en centimètres.
        
        Args:
            pixels: Valeur en pixels
            dpi: DPI (dots per inch)
            
        Returns:
            Valeur en centimètres
        """
        inches = pixels / dpi
        cm = inches * 2.54
        logger.debug(f"Conversion: {pixels}px = {cm:.2f}cm à {dpi} DPI")
        return cm
    
    @staticmethod
    def points_to_pixels(points: float, dpi: int = DEFAULT_DPI) -> int:
        """
        Convertir des points en pixels.
        
        Args:
            points: Valeur en points
            dpi: DPI
            
        Returns:
            Valeur en pixels
        """
        # 1 point = 1/72 inch
        inches = points / 72
        pixels = int(inches * dpi)
        logger.debug(f"Conversion: {points}pt = {pixels}px à {dpi} DPI")
        return pixels
    
    @staticmethod
    def get_image_info(image_path: str) -> Optional[dict]:
        """
        Obtenir des informations sur une image.
        
        Args:
            image_path: Chemin de l'image
            
        Returns:
            Dictionnaire avec les informations ou None si erreur
        """
        try:
            pil_module = lazy_imports.get_pil()
            if not pil_module:
                return None
            
            with pil_module['Image'].open(image_path) as img:
                info = {
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'width': img.width,
                    'height': img.height
                }
                
                # Ajouter les informations DPI si disponibles
                if hasattr(img, 'info') and 'dpi' in img.info:
                    info['dpi'] = img.info['dpi']
                
                logger.debug(f"Informations image {image_path}: {info}")
                return info
                
        except Exception as e:
            logger.error(f"Erreur obtention info image {image_path}: {e}")
            return None
