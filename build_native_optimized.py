#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour compiler l'application SOMACA NATIVE en .exe
Application native Windows avec pywebview - AUCUNE DÉPENDANCE EXTERNE
Développé par: IMAD ELberrouagui
"""

import PyInstaller.__main__
import os
import sys

def build_native_exe():
    """Compiler l'application native en .exe avec pywebview"""
    
    # Chemin vers l'icône QR (avec underscores)
    icon_path = os.path.join(os.path.dirname(__file__), 'qr_icone_ultra_optimized.ico')
    
    # Arguments pour PyInstaller
    args = [
        'app_native.py',                        # Script principal
        '--onefile',                            # Un seul fichier .exe
        '--windowed',                           # Pas de console
        '--name=SOMACA_Native',                 # Nom de l'exécutable
        '--icon=' + icon_path,
        '--manifest=app.manifest',                 # Manifeste DPI
        '--version-file=version.txt',              # Informations de version                  # Icône de l'application
        '--add-data=web;web',                   # Inclure le dossier web
        '--distpath=dist',                      # Dossier de sortie
        '--workpath=build',                     # Dossier de travail
        '--specpath=.',                         # Fichier .spec
        '--clean',                              # Nettoyer avant compilation
        '--noconfirm',                          # Pas de confirmation
        '--hidden-import=webview',              # Import caché pour webview
        '--hidden-import=pandas',               # Import caché pour pandas
        '--hidden-import=qrcode',               # Import caché pour qrcode
        '--hidden-import=barcode',              # Import caché pour barcode
        '--hidden-import=barcode.codex',        # Import caché pour Code128
        '--hidden-import=barcode.writer',       # Import caché pour ImageWriter
        '--hidden-import=barcode.base',         # Import caché pour base barcode
        '--hidden-import=PIL',                  # Import caché pour Pillow
        '--hidden-import=PIL.Image',            # Import caché pour PIL.Image
        '--hidden-import=PIL.ImageDraw',        # Import caché pour PIL.ImageDraw
        '--hidden-import=openpyxl',             # Import caché pour openpyxl
        '--hidden-import=openpyxl.drawing',     # Import caché pour openpyxl.drawing
        '--hidden-import=openpyxl.drawing.image', # Import caché pour openpyxl.drawing.image
        '--hidden-import=tkinter',              # Import caché pour tkinter
        '--hidden-import=win32print',           # Import caché pour win32print
        '--hidden-import=win32api',             # Import caché pour win32api
        '--hidden-import=tempfile',             # Import caché pour tempfile
        '--hidden-import=math',                 # Import caché pour math
        '--collect-all=webview',                # Collecter tous les modules webview
        '--collect-all=win32print',             # Collecter tous les modules win32print
        '--collect-all=barcode',                # Collecter tous les modules barcode
    ]
    
    print("🚀 Compilation de l'application SOMACA NATIVE en .exe...")
    print(f"📁 Icône utilisée: {icon_path}")
    print("⏳ Veuillez patienter...")
    print("=" * 60)
    
    try:
        # Lancer PyInstaller
        PyInstaller.__main__.run(args)
        
        print("=" * 60)
        print("✅ Compilation terminée avec succès !")
        print("📁 L'exécutable se trouve dans le dossier 'dist'")
        print("🎯 Nom du fichier: SOMACA_Native.exe")
        
        # Vérifier si le fichier existe
        exe_path = os.path.join('dist', 'SOMACA_Native.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📊 Taille du fichier: {size_mb:.1f} MB")
            print("🎉 Application native prête !")
            print("✅ AUCUNE DÉPENDANCE EXTERNE REQUISE")
            print("✅ Fonctionne sur n'importe quel Windows")
            print("✅ Interface native avec Edge WebView2")
        
    except Exception as e:
        print(f"❌ Erreur lors de la compilation: {e}")
        return False
    
    return True

def create_installer_script():
    """Créer un script d'installation pour l'app native"""
    installer_content = """
@echo off
echo ========================================
echo    SOMACA - Generateur de Codes-Barres
echo    Version NATIVE (PyWebView)
echo    Installation automatique
echo ========================================
echo.

echo Installation des dependances Python...
pip install pywebview pandas openpyxl qrcode python-barcode Pillow pyinstaller

echo.
echo Creation de l'executable NATIVE...
python build_native.py

echo.
echo ========================================
echo Installation terminee !
echo L'executable NATIF se trouve dans le dossier 'dist/'
echo Nom: SOMACA_Native.exe
echo ========================================
echo.
echo AVANTAGES de cette version:
echo - Application 100%% native Windows
echo - Aucune dependance externe
echo - Interface identique a la version web
echo - Fonctionne sur tous les Windows
echo - Utilise Edge WebView2 integre
echo ========================================
pause
"""
    
    with open("installer_native.bat", "w", encoding="utf-8") as f:
        f.write(installer_content)
    
    print("✅ Script d'installation NATIVE créé: installer_native.bat")

def main():
    """Fonction principale"""
    print("🏭 SOMACA - Générateur de Codes-Barres")
    print("🔧 Construction de l'exécutable NATIVE")
    print("🌟 Version PyWebView - Interface native Windows")
    print("=" * 60)
    
    try:
        # Vérifier que nous sommes dans le bon dossier
        if not os.path.exists("app_native.py"):
            print("❌ Erreur: app_native.py non trouvé")
            print("Assurez-vous d'être dans le dossier somaca_web_app")
            return
        
        # Créer l'exécutable
        if build_native_exe():
            print("\n🎉 Succès !")
            print("📁 Votre application SOMACA_Native.exe est prête dans le dossier 'dist/'")
            print("🚀 Vous pouvez maintenant distribuer ce fichier")
            print("✅ AUCUNE INSTALLATION REQUISE sur les machines cibles")
            print("✅ Fonctionne sur Windows 10/11 avec Edge WebView2")
        
        # Créer le script d'installation
        create_installer_script()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("💡 Assurez-vous d'avoir Python et pip installés")

if __name__ == '__main__':
    main()
