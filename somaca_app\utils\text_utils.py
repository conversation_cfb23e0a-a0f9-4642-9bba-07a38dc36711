#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Text Utilities - SOMACA

Utilitaires pour le nettoyage et la transformation de texte.
Spécialisé pour la compatibilité avec les codes-barres et QR codes.
"""

import re
import logging
from typing import Optional, Dict, Any
from ..config.constants import ACCENT_REPLACEMENTS, QR_PROBLEMATIC_CHARS

logger = logging.getLogger(__name__)


class TextCleaner:
    """
    Classe utilitaire pour nettoyer et formater le texte pour les codes-barres et QR codes.
    
    Les codes-barres Code128 ne supportent que l'ASCII (0-127).
    Les QR codes supportent l'UTF-8 mais certains caractères peuvent poser problème.
    """
    
    @staticmethod
    def is_ascii_compatible(text: str) -> bool:
        """
        Vérifier si le texte ne contient que des caractères compatibles avec Code128.
        
        Args:
            text: Texte à vérifier
            
        Returns:
            True si compatible ASCII, False sinon
        """
        try:
            text.encode('ascii')
            return True
        except UnicodeEncodeError:
            return False
    
    @staticmethod
    def clean_for_barcode(text: str) -> str:
        """
        Nettoyer le texte pour le rendre compatible avec Code128.
        
        Remplace les caractères accentués par leurs équivalents ASCII
        et supprime tous les caractères non-ASCII restants.
        
        Args:
            text: Texte à nettoyer
            
        Returns:
            Texte nettoyé compatible Code128
        """
        if not text:
            return ""
        
        # Convertir en string si ce n'est pas déjà le cas
        text = str(text)
        
        # Remplacer les caractères accentués
        cleaned_text = text
        for accented, ascii_char in ACCENT_REPLACEMENTS.items():
            cleaned_text = cleaned_text.replace(accented, ascii_char)
        
        # Supprimer tous les caractères non-ASCII restants
        cleaned_text = ''.join(char for char in cleaned_text if ord(char) < 128)
        
        logger.debug(f"Texte nettoyé pour code-barres: '{text}' -> '{cleaned_text}'")
        return cleaned_text
    
    @staticmethod
    def clean_for_qr(data: str) -> str:
        """
        Nettoyer les données pour les QR codes.
        
        Les QR codes supportent l'UTF-8, mais on nettoie les caractères
        de séparation problématiques pour une meilleure compatibilité.
        
        Args:
            data: Données à nettoyer
            
        Returns:
            Données nettoyées pour QR code
        """
        if not isinstance(data, str):
            data = str(data)
        
        # Nettoyer les données pour une compatibilité maximale
        clean_data = data.strip()
        
        # Remplacer les caractères problématiques
        for problematic, replacement in QR_PROBLEMATIC_CHARS.items():
            clean_data = clean_data.replace(problematic, replacement)
        
        # Nettoyer les espaces multiples
        clean_data = re.sub(r'\s+', ' ', clean_data)
        
        logger.debug(f"Données nettoyées pour QR: '{data[:50]}...' -> '{clean_data[:50]}...'")
        return clean_data
    
    @staticmethod
    def format_qr_data_multiline(data: str) -> str:
        """
        Formater les données QR en format multi-lignes compatible iOS/Android.
        
        Args:
            data: Données à formater
            
        Returns:
            Données formatées en multi-lignes
        """
        try:
            if isinstance(data, str):
                # Nettoyer les données pour une compatibilité maximale
                clean_data = data.strip()
                
                # Garder les retours à la ligne pour le format Nom=Valeur
                # Mais nettoyer les autres caractères problématiques
                clean_data = clean_data.replace('\r', '')    # Supprimer retours chariot Windows
                clean_data = clean_data.replace('\t', ' ')   # Remplacer tabulations par espaces
                
                # Nettoyer les espaces multiples mais garder les \n
                lines = clean_data.split('\n')
                cleaned_lines = []
                for line in lines:
                    # Nettoyer chaque ligne individuellement
                    clean_line = re.sub(r'\s+', ' ', line.strip())
                    if clean_line:  # Ignorer les lignes vides
                        cleaned_lines.append(clean_line)
                
                result = '\n'.join(cleaned_lines)
                logger.debug(f"Données QR multi-lignes formatées: {len(cleaned_lines)} lignes")
                return result
            else:
                return str(data)
        except Exception as e:
            logger.error(f"Erreur formatage données QR multi-lignes: {e}")
            return str(data)
    
    @staticmethod
    def create_barcode_data(col1: Any, col2: Any, separator: str = "-") -> str:
        """
        Créer des données de code-barres à partir de deux colonnes.
        
        Args:
            col1: Première colonne
            col2: Deuxième colonne  
            separator: Séparateur entre les colonnes
            
        Returns:
            Données nettoyées pour code-barres
        """
        # Nettoyer chaque colonne individuellement
        clean_col1 = TextCleaner.clean_for_barcode(str(col1) if col1 is not None else "")
        clean_col2 = TextCleaner.clean_for_barcode(str(col2) if col2 is not None else "")
        
        # Combiner avec le séparateur
        barcode_data = f"{clean_col1}{separator}{clean_col2}"
        
        logger.debug(f"Données code-barres créées: '{barcode_data}'")
        return barcode_data
    
    @staticmethod
    def create_qr_data_from_row(row_data: list, headers: list = None) -> str:
        """
        Créer des données QR à partir d'une ligne complète.
        
        Args:
            row_data: Données de la ligne
            headers: En-têtes des colonnes (optionnel)
            
        Returns:
            Données formatées pour QR code
        """
        if not row_data:
            return ""
        
        qr_data_parts = []
        
        if headers and len(headers) == len(row_data):
            # Format Nom=Valeur si on a les en-têtes
            for i, value in enumerate(row_data):
                if value is not None and str(value).strip():
                    qr_data_parts.append(f"{headers[i]}={value}")
        else:
            # Format simple séparé par des tirets
            for value in row_data:
                if value is not None and str(value).strip():
                    qr_data_parts.append(str(value).strip())
        
        # Joindre avec des retours à la ligne pour iOS
        qr_data = "\n".join(qr_data_parts)
        
        # Nettoyer le résultat final
        clean_qr_data = TextCleaner.clean_for_qr(qr_data)
        
        logger.debug(f"Données QR créées à partir de {len(row_data)} colonnes")
        return clean_qr_data
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 30, suffix: str = "...") -> str:
        """
        Tronquer le texte à une longueur maximale.
        
        Args:
            text: Texte à tronquer
            max_length: Longueur maximale
            suffix: Suffixe à ajouter si tronqué
            
        Returns:
            Texte tronqué
        """
        if not text:
            return ""
        
        text = str(text)
        if len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
