#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOMACA - Générateur de Codes-Barres NATIVE
Application native Windows avec interface HTML identique

Développé par: Imad Elberrouagui
Contributeurs:
- [Votre nom ici] - [Description de vos contributions]
- [Autre développeur] - [Description des contributions]

Version: 2.0 - Native Desktop Application
"""

# Configuration de l'encodage pour Windows - Compatible PyInstaller
import sys
import os

# Solution pour PyInstaller : ne pas modifier stdout/stderr
# PyInstaller gère déjà l'encodage correctement

# Imports optimisés pour démarrage ultra-rapide
import webview
import sys
import threading
import time
from functools import lru_cache
import io
import json
import tempfile
import math
import concurrent.futures

# Imports différés pour accélérer le démarrage
_pandas = None
_qrcode = None
_barcode = None
_openpyxl = None
_PIL = None
_tkinter = None
_win32 = None

def get_pandas():
    global _pandas
    if _pandas is None:
        import pandas as pd
        _pandas = pd
    return _pandas

def get_qrcode():
    global _qrcode
    if _qrcode is None:
        import qrcode
        _qrcode = qrcode
    return _qrcode

def get_barcode():
    global _barcode
    if _barcode is None:
        import barcode
        from barcode.writer import ImageWriter
        _barcode = {'module': barcode, 'ImageWriter': ImageWriter}
    return _barcode

def get_openpyxl():
    global _openpyxl
    if _openpyxl is None:
        from openpyxl import Workbook, load_workbook
        from openpyxl.drawing.image import Image as OpenpyxlImage
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        _openpyxl = {
            'Workbook': Workbook,
            'load_workbook': load_workbook,
            'OpenpyxlImage': OpenpyxlImage,
            'Font': Font,
            'PatternFill': PatternFill,
            'Alignment': Alignment,
            'Border': Border,
            'Side': Side
        }
    return _openpyxl

def get_PIL():
    global _PIL
    if _PIL is None:
        from PIL import Image
        _PIL = Image
    return _PIL

def get_tkinter():
    global _tkinter
    if _tkinter is None:
        import tkinter as tk
        from tkinter import filedialog, messagebox
        _tkinter = {'tk': tk, 'filedialog': filedialog, 'messagebox': messagebox}
    return _tkinter

def get_win32():
    global _win32
    if _win32 is None:
        import win32print
        import win32api
        _win32 = {'print': win32print, 'api': win32api}
    return _win32

# Désactiver la console pour éviter l'ouverture d'une fenêtre CMD
def disable_console():
    """Désactiver la sortie console pour éviter l'ouverture d'une fenêtre CMD"""
    try:
        # Rediriger stdout et stderr vers un fichier temporaire ou /dev/null
        if os.name == 'nt':  # Windows
            # Rediriger vers NUL (équivalent de /dev/null sur Windows)
            sys.stdout = open(os.devnull, 'w')
            sys.stderr = open(os.devnull, 'w')
    except:
        pass

# Appeler la fonction de désactivation de la console
disable_console()

class SomacaBarcodeGenerator:
    """
    Classe principale OPTIMISÉE pour la génération ultra-rapide de codes-barres et QR codes

    Développé par: Imad Elberrouagui - OPTIMISÉ POUR VITESSE MAXIMALE
    Modifiable par les contributeurs - Ajoutez votre nom ci-dessus
    """
    def __init__(self):
        self.input_file = ""
        self.output_folder = ""
        self.progress = 0
        self.is_cancelled = False
        self.current_thread = None
        self.window = None
        self.last_generated_file = None

        # Cache pour optimiser les performances - NOUVEAU
        self._barcode_cache = {}
        self._qr_cache = {}
        self._style_cache = {}
        self._code128_class = None

        # Cache pour les images Renault (améliore les performances)
        self._renault_cache = {}
        self._qr_config = None

        # Pool de threads pour génération parallèle - NOUVEAU
        self._thread_pool = None
        self._max_workers = min(8, (os.cpu_count() or 1) + 4)

    def cancel_operation(self):
        """Annuler l'opération en cours"""
        try:
            print("Demande d'annulation recue")
            self.is_cancelled = True

            # Si un thread est en cours, essayer de l'arrêter proprement
            if self.current_thread and self.current_thread.is_alive():
                print("Arrêt du thread en cours...")
                # Le thread vérifiera self.is_cancelled et s'arrêtera

            return {"success": True, "message": "Opération annulée"}

        except Exception as e:
            print(f"Erreur lors de l'annulation: {e}")
            return {"success": False, "message": f"Erreur lors de l'annulation: {str(e)}"}

    def set_input_file(self, file_path):
        """Définir le fichier d'entrée"""
        print(f"Tentative de définition du fichier: {file_path}")

        # Si c'est juste un nom de fichier, essayer de le trouver dans plusieurs endroits
        if not os.path.isabs(file_path) and not os.path.exists(file_path):
            search_paths = [
                os.getcwd(),
                os.path.dirname(os.path.abspath(__file__)),
                os.path.expanduser("~"),
                os.path.expanduser("~/Desktop"),
                os.path.expanduser("~/Documents")
            ]
            
            for search_path in search_paths:
                full_path = os.path.join(search_path, file_path)
                if os.path.exists(full_path):
                    file_path = full_path
                    print(f"Fichier trouvé: {file_path}")
                    break

        if os.path.exists(file_path):
            self.input_file = os.path.abspath(file_path)
            print(f"Fichier d'entrée défini: {self.input_file}")
            return {"success": True, "message": f"Fichier sélectionné: {self.input_file}"}
        else:
            print(f"Fichier non trouvé: {file_path}")
            return {"success": False, "message": f"Fichier non trouvé: {file_path}"}

    def set_output_folder(self, folder_path):
        """Définir le dossier de sortie"""
        print(f"Tentative de définition du dossier: {folder_path}")

        if not folder_path:
            return {"success": False, "message": "Chemin de dossier vide"}

        try:
            # Créer le dossier s'il n'existe pas
            if not os.path.exists(folder_path):
                os.makedirs(folder_path, exist_ok=True)
                print(f"Dossier créé: {folder_path}")

            self.output_folder = os.path.abspath(folder_path)
            print(f"Dossier de sortie défini: {self.output_folder}")
            return {"success": True, "message": f"Dossier sélectionné: {self.output_folder}"}

        except Exception as e:
            print(f"Erreur création dossier: {e}")
            return {"success": False, "message": f"Erreur création dossier: {str(e)}"}

    def update_progress_callback(self, percentage):
        """Callback pour mettre à jour la progression EN TEMPS RÉEL"""
        self.progress = percentage
        print(f"Progression: {percentage}%")

        # Mettre à jour l'interface en temps réel via pywebview
        if self.window:
            try:
                self.window.evaluate_js(f"update_progress({percentage})")
            except Exception as e:
                print(f"Erreur mise à jour progression: {e}")

    def generation_complete_callback(self, result):
        """Callback quand la génération est terminée"""
        print(f"Génération terminée: {result}")

    @lru_cache(maxsize=1000)
    def generate_barcode_image(self, data):
        """Génère une image de code-barre ULTRA-RAPIDE avec cache intelligent"""
        try:
            # Cache check pour éviter la régénération
            cache_key = f"barcode_{hash(str(data))}"
            if cache_key in self._barcode_cache:
                return self._barcode_cache[cache_key]

            # NETTOYER D'ABORD les données pour les rendre compatibles
            cleaned_data = self.clean_text_for_barcode(str(data))

            # Vérifications rapides
            if not cleaned_data or cleaned_data.strip() == "":
                return None

            if not self.is_ascii_compatible(cleaned_data):
                return None

            # Import différé optimisé pour vitesse
            barcode_module = get_barcode()
            if not barcode_module:
                return None

            # Cache optimisé : Réutiliser la classe code128
            if self._code128_class is None:
                try:
                    self._code128_class = barcode_module['module'].get_barcode_class('code128')
                except Exception as e:
                    return None

            # Génération ultra-rapide
            try:
                barcode_instance = self._code128_class(cleaned_data, writer=barcode_module['ImageWriter']())
                buffer = io.BytesIO()
                barcode_instance.write(buffer)
                buffer.seek(0)
            except Exception as e:
                return None

            # Import différé OpenpyXL optimisé
            openpyxl_module = get_openpyxl()
            if not openpyxl_module:
                return None

            try:
                img = openpyxl_module['OpenpyxlImage'](buffer)
                # Dimensions pour étiquettes Renault : 4.4cm x 2.2cm (largeur x hauteur)
                img.width = 166   # 4.4cm à 96 DPI (4.4 * 37.8 = 166 pixels)
                img.height = 83   # 2.2cm à 96 DPI (2.2 * 37.8 = 83 pixels)

                # Mettre en cache pour réutilisation ULTRA-RAPIDE
                self._barcode_cache[cache_key] = img
                return img
            except Exception as e:
                return None

        except Exception as e:
            return None

    def is_ascii_compatible(self, text):
        """Vérifier si le texte ne contient que des caractères compatibles avec Code128"""
        try:
            # Code128 supporte les caractères ASCII de 0 à 127
            text.encode('ascii')
            return True
        except UnicodeEncodeError:
            return False

    def clean_text_for_barcode(self, text):
        """Nettoyer le texte pour le rendre compatible avec Code128"""
        if not text:
            return ""

        # Convertir en string si ce n'est pas déjà le cas
        text = str(text)

        # Remplacer les caractères accentués par leurs équivalents ASCII
        replacements = {
            'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ä': 'a', 'å': 'a',
            'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e',
            'ì': 'i', 'í': 'i', 'î': 'i', 'ï': 'i',
            'ò': 'o', 'ó': 'o', 'ô': 'o', 'õ': 'o', 'ö': 'o',
            'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u',
            'ý': 'y', 'ÿ': 'y',
            'ç': 'c', 'ñ': 'n',
            'À': 'A', 'Á': 'A', 'Â': 'A', 'Ã': 'A', 'Ä': 'A', 'Å': 'A',
            'È': 'E', 'É': 'E', 'Ê': 'E', 'Ë': 'E',
            'Ì': 'I', 'Í': 'I', 'Î': 'I', 'Ï': 'I',
            'Ò': 'O', 'Ó': 'O', 'Ô': 'O', 'Õ': 'O', 'Ö': 'O',
            'Ù': 'U', 'Ú': 'U', 'Û': 'U', 'Ü': 'U',
            'Ý': 'Y', 'Ÿ': 'Y',
            'Ç': 'C', 'Ñ': 'N'
        }

        cleaned_text = str(text)
        for accented, ascii_char in replacements.items():
            cleaned_text = cleaned_text.replace(accented, ascii_char)

        # Supprimer tous les caractères non-ASCII restants
        cleaned_text = ''.join(char for char in cleaned_text if ord(char) < 128)

        return cleaned_text

    def generate_qr_image(self, data):
        """Génère une image QR code ULTRA-RAPIDE avec cache intelligent"""
        try:
            # Cache check pour éviter la régénération
            cache_key = f"qr_{hash(str(data))}"
            if cache_key in self._qr_cache:
                return self._qr_cache[cache_key]

            # Import différé optimisé
            qrcode_module = get_qrcode()
            if not qrcode_module:
                return None

            # Configuration optimisée avec cache - bordure réduite
            if self._qr_config is None:
                self._qr_config = {
                    'version': None,
                    'error_correction': qrcode_module.constants.ERROR_CORRECT_M,
                    'box_size': 8,
                    'border': 0,  # SANS BORDURE - bordure supprimée complètement
                }

            qr = qrcode_module.QRCode(**self._qr_config)

            # Nettoyer et formater les données
            clean_data = self._format_qr_data(data)
            qr.add_data(clean_data)
            qr.make(fit=True)

            # Génération rapide
            qr_img = qr.make_image(fill_color="black", back_color="white")
            buffer = io.BytesIO()
            qr_img.save(buffer, format='PNG')
            buffer.seek(0)

            # Import différé OpenpyXL
            openpyxl_module = get_openpyxl()
            if not openpyxl_module:
                return None

            img = openpyxl_module['OpenpyxlImage'](buffer)
            img.width = 95   # 2.5cm exactement (2.5 * 37.8 = 95 pixels à 96 DPI)
            img.height = 95  # 2.5cm exactement

            # Mettre en cache pour réutilisation ULTRA-RAPIDE
            self._qr_cache[cache_key] = img
            return img
        except Exception as e:
            return None

    def generate_framed_image(self, row_data, row_index):
        """Génère une image 6cm x 3cm avec cadre Renault, QR code et texte descriptif"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import qrcode

            # Dimensions finales : 6cm x 3cm à 300 DPI
            width_px = int(6 * 300 / 2.54)  # 709 pixels
            height_px = int(3 * 300 / 2.54)  # 354 pixels

            # Créer l'image de base avec fond blanc
            img = Image.new('RGB', (width_px, height_px), 'white')
            draw = ImageDraw.Draw(img)

            # Dessiner le cadre
            border_width = 3
            draw.rectangle([0, 0, width_px-1, height_px-1], outline='black', width=border_width)

            # Zone pour le logo Renault (coin supérieur gauche)
            logo_size = 60
            try:
                # Dessiner un rectangle pour représenter le logo Renault
                draw.rectangle([10, 10, 10+logo_size, 10+logo_size], outline='blue', width=2)
                draw.text((15, 30), "RENAULT", fill='blue')
            except:
                # Si pas de logo, juste le texte
                draw.text((10, 10), "RENAULT", fill='blue')

            # Générer le QR code avec TOUTES les données de la ligne
            all_data = ' | '.join([str(cell) for cell in row_data if cell is not None and str(cell).strip()])
            formatted_qr_data = self._format_qr_data(all_data)

            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=8,
                border=0,  # SANS BORDURE
            )
            qr.add_data(formatted_qr_data)
            qr.make(fit=True)

            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Redimensionner le QR code (environ 2cm x 2cm)
            qr_size = int(2 * 300 / 2.54)  # 236 pixels
            qr_img = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)

            # Positionner le QR code au centre-droit
            qr_x = width_px - qr_size - 20
            qr_y = (height_px - qr_size) // 2
            img.paste(qr_img, (qr_x, qr_y))

            # Texte descriptif (2 premières cellules comme pour les codes-barres)
            if len(row_data) >= 2:
                text_parts = []
                for i in range(min(2, len(row_data))):
                    if row_data[i] is not None and str(row_data[i]).strip():
                        text_parts.append(str(row_data[i]).strip())

                descriptive_text = '-'.join(text_parts)
            else:
                descriptive_text = str(row_data[0]) if row_data else f"Item {row_index}"

            # Limiter la longueur du texte
            if len(descriptive_text) > 30:
                descriptive_text = descriptive_text[:27] + "..."

            # Positionner le texte au centre-gauche
            try:
                # Essayer d'utiliser une police système
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()

            text_x = 80
            text_y = height_px // 2 - 10
            draw.text((text_x, text_y), descriptive_text, fill='black', font=font)

            # Convertir en bytes pour openpyxl
            buffer = io.BytesIO()
            img.save(buffer, format='PNG', dpi=(300, 300))
            buffer.seek(0)

            # Import différé openpyxl pour l'image
            openpyxl_module = get_openpyxl()
            if not openpyxl_module:
                return None

            # Créer l'objet image pour Excel
            excel_img = openpyxl_module['Image'](buffer)

            # Dimensions : 6cm x 3cm
            excel_img.width = int(6 * 28.35)  # 170 points
            excel_img.height = int(3 * 28.35)  # 85 points

            return excel_img

        except Exception as e:
            print(f"Erreur génération image encadrée: {e}")
            return None

    def generate_framed_pil_image(self, row_data, row_index):
        """Génère une image PIL 6cm x 3cm avec cadre Renault, QR code et texte descriptif"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import qrcode

            # Dimensions finales : 6cm x 3cm à 300 DPI
            width_px = int(6 * 300 / 2.54)  # 709 pixels
            height_px = int(3 * 300 / 2.54)  # 354 pixels

            # Créer l'image de base avec fond blanc
            img = Image.new('RGB', (width_px, height_px), 'white')
            draw = ImageDraw.Draw(img)

            # Dessiner le cadre
            border_width = 3
            draw.rectangle([0, 0, width_px-1, height_px-1], outline='black', width=border_width)

            # Zone pour le logo Renault (coin supérieur gauche)
            logo_size = 60
            try:
                # Dessiner un rectangle pour représenter le logo Renault
                draw.rectangle([10, 10, 10+logo_size, 10+logo_size], outline='blue', width=2)
                draw.text((15, 30), "RENAULT", fill='blue')
            except:
                # Si pas de logo, juste le texte
                draw.text((10, 10), "RENAULT", fill='blue')

            # Générer le QR code avec TOUTES les données de la ligne
            all_data = ' | '.join([str(cell) for cell in row_data if cell is not None and str(cell).strip()])
            formatted_qr_data = self._format_qr_data(all_data)

            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=8,
                border=0,  # SANS BORDURE
            )
            qr.add_data(formatted_qr_data)
            qr.make(fit=True)

            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Redimensionner le QR code (environ 2cm x 2cm)
            qr_size = int(2 * 300 / 2.54)  # 236 pixels
            qr_img = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)

            # Positionner le QR code au centre-droit
            qr_x = width_px - qr_size - 20
            qr_y = (height_px - qr_size) // 2
            img.paste(qr_img, (qr_x, qr_y))

            # Texte descriptif (2 premières cellules comme pour les codes-barres)
            if len(row_data) >= 2:
                text_parts = []
                for i in range(min(2, len(row_data))):
                    if row_data[i] is not None and str(row_data[i]).strip():
                        text_parts.append(str(row_data[i]).strip())

                descriptive_text = '-'.join(text_parts)
            else:
                descriptive_text = str(row_data[0]) if row_data else f"Item {row_index}"

            # Limiter la longueur du texte
            if len(descriptive_text) > 30:
                descriptive_text = descriptive_text[:27] + "..."

            # Positionner le texte au centre-gauche
            try:
                # Essayer d'utiliser une police système
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()

            text_x = 80
            text_y = height_px // 2 - 10
            draw.text((text_x, text_y), descriptive_text, fill='black', font=font)

            return img

        except Exception as e:
            print(f"Erreur génération image PIL encadrée: {e}")
            return None

    def _format_qr_data(self, data):
        """Formater les données QR pour une compatibilité maximale iOS/Android"""
        try:
            if isinstance(data, str):
                # Nettoyer les données pour une compatibilité maximale
                clean_data = data.strip()

                # Garder les retours à la ligne pour le format Nom=Valeur
                # Mais nettoyer les autres caractères problématiques
                clean_data = clean_data.replace('\r', '')    # Supprimer retours chariot Windows
                clean_data = clean_data.replace('\t', ' ')   # Remplacer tabulations par espaces

                # Nettoyer les espaces multiples mais garder les \n
                import re
                lines = clean_data.split('\n')
                cleaned_lines = []
                for line in lines:
                    # Nettoyer chaque ligne individuellement
                    clean_line = re.sub(r'\s+', ' ', line.strip())
                    if clean_line:  # Ignorer les lignes vides
                        cleaned_lines.append(clean_line)

                return '\n'.join(cleaned_lines)
            else:
                return str(data)
        except Exception as e:
            print(f"Erreur formatage données QR: {e}")
            return str(data)

    def _format_qr_data(self, data):
        """Formater les données QR pour une compatibilité maximale"""
        try:
            # Si les données contiennent des caractères spéciaux, les encoder proprement
            if isinstance(data, str):
                # Nettoyer les caractères problématiques mais garder les accents pour les QR codes
                # Les QR codes supportent l'UTF-8 contrairement aux codes-barres
                clean_data = data.strip()

                # Remplacer les caractères de séparation problématiques
                clean_data = clean_data.replace('|', ' - ')  # Remplacer | par -
                clean_data = clean_data.replace('\n', ' ')   # Remplacer retours à la ligne
                clean_data = clean_data.replace('\r', ' ')   # Remplacer retours chariot
                clean_data = clean_data.replace('\t', ' ')   # Remplacer tabulations

                # Nettoyer les espaces multiples
                import re
                clean_data = re.sub(r'\s+', ' ', clean_data)

                return clean_data
            else:
                return str(data)
        except Exception as e:
            print(f"Erreur formatage données QR: {e}")
            return str(data)

    def generate_qr_code_image(self, data):
        """Alias pour compatibilité"""
        return self.generate_qr_image(data)

    def get_logo_base64(self, background_color="black"):
        """Convertit le logo Renault en base64 selon la couleur de fond"""
        try:
            import base64

            # Choisir le logo selon la couleur de fond
            if background_color == "white":
                # Fond blanc -> logo noir
                logo_paths = ["renault_logo noir.png", "renault_noir.jpg", "renault_noir.png", "renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
                print(f"Recherche logo NOIR pour fond blanc...")
            else:
                # Fond noir -> logo blanc
                logo_paths = ["renault-logo blach.jpg", "renault_blanc.jpg", "renault_blanc.png", "renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
                print(f"Recherche logo BLANC pour fond noir...")

            for logo_path in logo_paths:
                if os.path.exists(logo_path):
                    print(f"Logo trouve: {logo_path}")
                    with open(logo_path, "rb") as f:
                        logo_data = f.read()
                    return base64.b64encode(logo_data).decode('utf-8')

            print(f"Aucun logo trouve pour fond {background_color}")
            return ""
        except Exception as e:
            print(f"Erreur conversion logo: {e}")
            return ""

    def generate_renault_barcode(self, barcode_data):
        """Génère code-barres Renault 4,5cm x 3cm avec 2 premières colonnes seulement"""
        try:
            print(f"Generation code-barres: {barcode_data}")
            import base64

            # Dimensions finales : 4,5cm x 3cm
            target_width_cm = 4.5
            target_height_cm = 3.0
            dpi = 300
            target_width_px = int(target_width_cm * dpi / 2.54)  # 531 pixels
            target_height_px = int(target_height_cm * dpi / 2.54)  # 354 pixels

            # Import différé barcode
            barcode_module = get_barcode()
            if not barcode_module:
                return ""

            # Générer code-barres
            code128 = barcode_module.get_barcode_class('code128')
            writer = barcode_module.writer.ImageWriter()
            writer.set_options({
                'module_width': 0.6,    # Barres plus larges
                'module_height': 35,    # Barres plus hautes
                'quiet_zone': 1,
                'dpi': dpi,
                'background': 'white',
                'foreground': 'black',
            })
            barcode_instance = code128(barcode_data, writer=writer)

            # Générer l'image
            buffer = io.BytesIO()
            barcode_instance.write(buffer)
            buffer.seek(0)

            # Import différé PIL
            PIL_module = get_PIL()
            if not PIL_module:
                return ""

            barcode_img = PIL_module.open(buffer)
            width, height = barcode_img.size

            # COUPER la partie texte (garder seulement les barres)
            cut_height = int(height * 0.7)
            barcode_only_bars = barcode_img.crop((0, 0, width, cut_height))

            # Redimensionner exactement à 4,5cm x 3cm
            try:
                # Essayer avec la nouvelle API PIL
                barcode_final = barcode_only_bars.resize((target_width_px, target_height_px), PIL_module.Resampling.LANCZOS)
            except AttributeError:
                # Fallback pour les anciennes versions de PIL
                barcode_final = barcode_only_bars.resize((target_width_px, target_height_px), PIL_module.LANCZOS)

            # Convertir en base64
            img_buffer = io.BytesIO()
            barcode_final.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            return base64.b64encode(img_buffer.getvalue()).decode('utf-8')

        except Exception as e:
            print(f"ERREUR code-barres Renault: {e}")
            return ""

    def generate_renault_qr(self, qr_data):
        """Génère QR code Renault 2cm x 2cm avec TOUTES les données"""
        try:
            print(f"Generation QR code: {qr_data[:50]}...")
            import base64

            # Import différé qrcode
            qrcode_module = get_qrcode()
            if not qrcode_module:
                return ""

            # Générer QR code
            qr = qrcode_module.QRCode(
                version=1,
                error_correction=qrcode_module.constants.ERROR_CORRECT_L,
                box_size=10,
                border=0,  # SANS BORDURE
            )
            qr.add_data(qr_data)
            qr.make(fit=True)

            # Créer l'image QR
            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Convertir en base64
            img_buffer = io.BytesIO()
            qr_img.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            return base64.b64encode(img_buffer.getvalue()).decode('utf-8')

        except Exception as e:
            print(f"Erreur QR code Renault: {e}")
            return ""

    def create_renault_etiquette(self, row_data, code_type="barcode"):
        """Créer étiquette Renault avec données de ligne Excel

        Args:
            row_data: Liste des données de la ligne Excel
            code_type: "barcode" ou "qr"
        """

        print(f"Creation etiquette {code_type} pour: {row_data[:2] if len(row_data) >= 2 else row_data}")
        logo_base64 = self.get_logo_base64()
        print(f"Logo charge: {'Oui' if logo_base64 else 'Non'}")

        # Vérifier qu'on a au moins 2 colonnes
        if len(row_data) < 2:
            print("Erreur: Il faut au moins 2 colonnes de donnees")
            return None

        # Les 2 premières colonnes pour l'affichage en bas avec séparation -
        display_text = f"{row_data[0]} - {row_data[1]}"

        if code_type == "barcode":
            # Code-barres: seulement les 2 premières colonnes (nettoyer les caractères spéciaux)
            clean_data1 = str(row_data[0]).replace('è', 'e').replace('é', 'e').replace('à', 'a').replace('ç', 'c')
            clean_data2 = str(row_data[1]).replace('è', 'e').replace('é', 'e').replace('à', 'a').replace('ç', 'c')
            barcode_data = f"{clean_data1}|{clean_data2}"
            code_base64 = self.generate_renault_barcode(barcode_data)
            code_title = "Code-barres"
            container_width = "4.5cm"
            container_height = "3cm"
            code_width = "4.5cm"
            code_height = "3cm"
        else:
            # QR code: TOUTES les données de la ligne
            qr_data = "|".join(str(item) for item in row_data)
            code_base64 = self.generate_renault_qr(qr_data)
            code_title = "QR Code"
            container_width = "2.2cm"  # Zone blanche finale
            container_height = "3.0cm"  # Zone blanche finale
            code_width = "2cm"  # QR code centré
            code_height = "2cm"

        html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault - {code_title}</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      /* Cadre final : 7,0cm x 3,9cm */
      width: 7.0cm;
      height: 3.9cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 6px;
    }}
    .logo img {{
      height: 35px;  /* Logo petit pour laisser de la place */
    }}
    .code-number {{
      font-size: 10px;  /* Texte petit */
      font-weight: bold;
      text-align: center;
      margin-top: 3px;
    }}
    .code-container {{
      /* Zone blanche selon le type */
      width: {container_width};
      height: {container_height};
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .code-img {{
      /* Code - dimensions exactes */
      width: {code_width};
      height: {code_height};
      object-fit: contain;
      display: block;
      border-radius: 4px;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="code-container">
        <img class="code-img" src="data:image/png;base64,{code_base64}" alt="{code_title}">
      </div>
      <div class="code-number">{display_text}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""

        return html_content

    def cleanup_open_files(self):
        """Nettoyer tous les fichiers ouverts pour éviter les conflits"""
        try:
            import gc
            # Forcer la collecte des déchets pour libérer les fichiers
            gc.collect()
            print("🧹 Nettoyage des fichiers ouverts effectué")
        except Exception as e:
            print(f"Erreur nettoyage: {e}")

    def generate_codes(self, generate_barcodes=True, generate_qr=True):
        """Générer un fichier Excel avec codes-barres et QR codes"""
        try:
            # Nettoyer les fichiers ouverts avant de commencer
            self.cleanup_open_files()

            if not self.input_file or not self.output_folder:
                return {"success": False, "message": "Veuillez sélectionner un fichier et un dossier de destination"}

            # Vérifier que le fichier existe
            if not os.path.exists(self.input_file):
                return {"success": False, "message": f"Fichier non trouvé: {self.input_file}"}

            # Vérifier que le dossier de sortie existe
            if not os.path.exists(self.output_folder):
                return {"success": False, "message": f"Dossier non trouvé: {self.output_folder}"}

            print(f"Début génération - Codes-barres: {generate_barcodes}, QR: {generate_qr}")
            print(f"Fichier: {self.input_file}")
            print(f"Sortie: {self.output_folder}")

            # Import différé pandas pour vitesse ULTRA-RAPIDE
            pandas_module = get_pandas()
            if not pandas_module:
                return {"success": False, "message": "Erreur chargement pandas"}

            # Lire le fichier Excel RAPIDEMENT avec gestion des ressources
            try:
                # Utiliser un context manager pour s'assurer que le fichier est fermé
                with open(self.input_file, 'rb') as file:
                    df = pandas_module.read_excel(file)
                print(f"Fichier lu: {len(df)} lignes, {len(df.columns)} colonnes")

                # Forcer la libération des ressources
                import gc
                gc.collect()

            except Exception as e:
                return {"success": False, "message": f"Erreur lecture fichier Excel: {str(e)}"}

            if df.empty:
                return {"success": False, "message": "Le fichier Excel est vide"}

            # Sauvegarder les en-têtes originaux
            original_headers = df.columns.tolist()
            print(f"En-têtes: {original_headers}")

            # Convertir en données
            data = df.values.tolist()

            # Import différé openpyxl pour vitesse ULTRA-RAPIDE
            openpyxl_module = get_openpyxl()
            if not openpyxl_module:
                return {"success": False, "message": "Erreur chargement openpyxl"}

            # Créer le nouveau workbook RAPIDEMENT
            workbook = openpyxl_module['Workbook']()
            worksheet = workbook.active
            worksheet.title = "Données avec Codes"

            # Styles optimisés avec cache ULTRA-RAPIDE
            if 'header_styles' not in self._style_cache:
                self._style_cache['header_styles'] = {
                    'font': openpyxl_module['Font'](name='Segoe UI', size=11, bold=True, color='FFFFFF'),
                    'fill': openpyxl_module['PatternFill'](start_color='003366', end_color='003366', fill_type='solid'),
                    'alignment': openpyxl_module['Alignment'](horizontal='center', vertical='center'),
                    'thin_border': openpyxl_module['Side'](border_style="thin", color="CCCCCC"),
                    'fill_color': openpyxl_module['PatternFill'](start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
                }

            styles = self._style_cache['header_styles']
            header_border = openpyxl_module['Border'](
                left=styles['thin_border'], right=styles['thin_border'],
                top=styles['thin_border'], bottom=styles['thin_border']
            )

            # Préparer les en-têtes
            headers = original_headers.copy()
            barcode_col = None
            qr_col = None

            if generate_barcodes:
                headers.append("Code-Barres")
                barcode_col = len(headers)

            if generate_qr:
                headers.append("QR Code")
                qr_col = len(headers)

            # Écrire les en-têtes RAPIDEMENT
            for col_num, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col_num, value=header)
                cell.font = styles['font']
                cell.fill = styles['fill']
                cell.alignment = styles['alignment']
                cell.border = header_border

            print(f"En-têtes écrits: {len(headers)} colonnes")

            # Traitement des données avec progression
            total_rows = len(data)
            for row_index, row_data in enumerate(data):
                if self.is_cancelled:
                    return {"success": False, "message": "Opération annulée"}

                row_num = row_index + 2  # +2 car Excel commence à 1 et on a les en-têtes

                # Écrire les données originales RAPIDEMENT
                for col_index, value in enumerate(row_data):
                    cell = worksheet.cell(row=row_num, column=col_index + 1, value=value)
                    cell.border = openpyxl_module['Border'](
                        left=styles['thin_border'], right=styles['thin_border'],
                        top=styles['thin_border'], bottom=styles['thin_border']
                    )

                    # Alignement du texte centré au milieu
                    cell.alignment = openpyxl_module['Alignment'](
                        horizontal='center',
                        vertical='center',
                        wrap_text=True  # Retour à la ligne automatique
                    )

                    # Police et style pour les données
                    cell.font = openpyxl_module['Font'](name='Segoe UI', size=10)

                    # Couleur alternée
                    if row_index % 2 == 0:
                        cell.fill = styles['fill_color']

                # Générer les codes basés sur les données de la ligne
                col1 = row_data[0] if len(row_data) > 0 else ""
                col2 = row_data[1] if len(row_data) > 1 else ""

                # Nettoyer les données pour les codes-barres (supprimer les accents)
                col1_clean = self.clean_text_for_barcode(col1)
                col2_clean = self.clean_text_for_barcode(col2)
                barcode_data = f"{col1_clean}-{col2_clean}"

                # QR Code : TOUTES les données de la ligne (format compatible iOS)
                qr_data_parts = []
                for i, value in enumerate(row_data):
                    if i < len(original_headers) and value is not None:
                        # Format simple compatible : Nom=Valeur
                        qr_data_parts.append(f"{original_headers[i]}={value}")
                qr_data = "\n".join(qr_data_parts)  # Format multi-lignes pour iOS

                # Code-barre (seulement si demandé) - OPTIMISÉ
                if generate_barcodes and barcode_col:
                    barcode_cell = worksheet.cell(row=row_num, column=barcode_col, value="")
                    barcode_cell.border = openpyxl_module['Border'](
                        left=styles['thin_border'], right=styles['thin_border'],
                        top=styles['thin_border'], bottom=styles['thin_border']
                    )

                    # Alignement centré pour les images
                    barcode_cell.alignment = openpyxl_module['Alignment'](horizontal='center', vertical='center')

                    if row_index % 2 == 0:
                        barcode_cell.fill = styles['fill_color']

                    # Générer l'image du code-barre avec données nettoyées
                    barcode_img = self.generate_barcode_image(barcode_data)

                    if barcode_img:
                        # Positionner l'image au centre de la cellule avec un petit décalage
                        col_letter = chr(64 + barcode_col)
                        barcode_img.anchor = f"{col_letter}{row_num}"

                        # Ajuster la position pour centrer parfaitement l'image dans la cellule
                        barcode_img.left = 12  # Décalage horizontal optimisé pour colonne largeur 18
                        barcode_img.top = 10   # Décalage vertical optimisé pour hauteur 2cm (56.7 points)

                        worksheet.add_image(barcode_img)
                    else:
                        # Si le code-barre ne peut pas être généré, mettre un texte d'info
                        barcode_cell.value = "Caractères non supportés"
                        barcode_cell.font = openpyxl_module['Font'](name='Segoe UI', size=8, italic=True)

                # QR Code (seulement si demandé) - OPTIMISÉ
                if generate_qr and qr_col:
                    qr_cell = worksheet.cell(row=row_num, column=qr_col, value="")
                    qr_cell.border = openpyxl_module['Border'](
                        left=styles['thin_border'], right=styles['thin_border'],
                        top=styles['thin_border'], bottom=styles['thin_border']
                    )

                    # Alignement centré pour les images
                    qr_cell.alignment = openpyxl_module['Alignment'](horizontal='center', vertical='center')

                    if row_index % 2 == 0:
                        qr_cell.fill = styles['fill_color']

                    # Générer l'image du QR code
                    qr_img = self.generate_qr_image(qr_data)
                    if qr_img:
                        # Positionner l'image au centre de la cellule avec un petit décalage
                        col_letter = chr(64 + qr_col)
                        qr_img.anchor = f"{col_letter}{row_num}"

                        # Ajuster la position pour centrer parfaitement l'image dans la cellule
                        qr_img.left = 10  # Décalage horizontal optimisé pour colonne largeur 12
                        qr_img.top = 10   # Décalage vertical optimisé pour hauteur 2cm (56.7 points)

                        worksheet.add_image(qr_img)

                # Mise à jour progression en temps réel (seulement le pourcentage)
                progress = ((row_index + 1) / total_rows) * 100
                self.update_progress_callback(int(progress))

            # Ajuster les largeurs des colonnes
            for col_num in range(1, len(headers) + 1):
                col_letter = chr(64 + col_num)

                if col_num == barcode_col:
                    # Colonne code-barres : taille standard fixe optimale
                    worksheet.column_dimensions[col_letter].width = 18
                elif col_num == qr_col:
                    # Colonne QR code : taille standard fixe optimale
                    worksheet.column_dimensions[col_letter].width = 12
                else:
                    # Colonnes de données : largeur automatique adaptée au contenu
                    max_length = len(str(headers[col_num - 1]))  # Longueur de l'en-tête

                    # Vérifier la longueur du contenu dans cette colonne
                    for row_data in data:
                        if col_num - 1 < len(row_data):
                            cell_value = str(row_data[col_num - 1]) if row_data[col_num - 1] is not None else ""
                            max_length = max(max_length, len(cell_value))

                    # Largeur automatique : s'adapte au contenu (minimum 10, maximum 40)
                    auto_width = min(max(max_length + 2, 10), 40)
                    worksheet.column_dimensions[col_letter].width = auto_width

            # Ajuster les hauteurs des lignes pour les images et le texte
            # En-tête : hauteur standard
            worksheet.row_dimensions[1].height = 30

            # Lignes de données : hauteur de 2cm (comme demandé)
            for row_num in range(2, total_rows + 2):
                # 2cm = 56.7 points (1cm = 28.35 points)
                worksheet.row_dimensions[row_num].height = 56.7

            # Sauvegarder le fichier
            base_name = os.path.splitext(os.path.basename(self.input_file))[0]
            output_filename = f"{base_name}_avec_codes.xlsx"
            output_path = os.path.join(self.output_folder, output_filename)

            # Gérer les conflits de fichiers
            if os.path.exists(output_path):
                # Fichier existe déjà - retourner un statut spécial pour déclencher la modal
                print(f"Fichier existe déjà: {output_path}")
                return {
                    "success": False,
                    "file_exists": True,
                    "filename": output_filename,
                    "message": f"Le fichier {output_filename} existe déjà"
                }

            # Sauvegarder le fichier
            print(f"Sauvegarde du fichier: {output_path}")
            workbook.save(output_path)
            workbook.close()

            # Stocker le fichier généré pour l'export
            self.last_generated_file = output_path
            # Aussi dans l'instance globale pour compatibilité
            generator.last_generated_file = output_path

            print(f"Fichier généré avec succès: {output_path}")

            # Vérifier que le fichier a bien été créé
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"Fichier créé - Taille: {file_size} bytes")
                return {
                    "success": True,
                    "message": f"Fichier généré avec succès !\nEmplacement: {output_path}\nTaille: {file_size} bytes",
                    "file_path": output_path,
                    "rows_processed": total_rows
                }
            else:
                return {"success": False, "message": f"Erreur: Le fichier n'a pas pu être créé à {output_path}"}

        except Exception as e:
            print(f"Erreur lors du traitement: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def generate_renault_etiquettes(self):
        """PRENDRE les codes du fichier Excel et demander le choix utilisateur"""
        try:
            print("[DEBUG] Verification fichier Excel avec codes...")

            # Vérifier qu'un fichier généré existe
            print(f"[DEBUG] Verification last_generated_file: {hasattr(self, 'last_generated_file')}")
            if hasattr(self, 'last_generated_file'):
                print(f"[DEBUG] last_generated_file value: {self.last_generated_file}")

            if not hasattr(self, 'last_generated_file') or not self.last_generated_file:
                print("[ERROR] Aucun fichier Excel genere trouve")
                return {"success": False, "message": "Veuillez d'abord generer un fichier Excel avec codes-barres/QR codes"}

            print(f"[DEBUG] Verification existence fichier: {self.last_generated_file}")
            if not os.path.exists(self.last_generated_file):
                print("[ERROR] Fichier Excel non trouve sur le disque")
                return {"success": False, "message": "Fichier Excel avec codes non trouve. Generez d'abord les codes."}

            print(f"[SUCCESS] Fichier trouve: {self.last_generated_file}")

            # Vérifier qu'il y a des images dans le fichier
            print("[DEBUG] Chargement openpyxl...")
            openpyxl_module = get_openpyxl()
            if not openpyxl_module:
                print("[ERROR] Erreur chargement openpyxl")
                return {"success": False, "message": "Erreur chargement openpyxl"}

            print("[DEBUG] Ouverture fichier Excel...")
            workbook = openpyxl_module['load_workbook'](self.last_generated_file)
            worksheet = workbook.active

            print(f"[DEBUG] Verification images dans le fichier...")
            print(f"[DEBUG] Nombre d'images: {len(worksheet._images) if hasattr(worksheet, '_images') else 'Pas d\'attribut _images'}")

            if not hasattr(worksheet, '_images') or not worksheet._images:
                workbook.close()
                print("[ERROR] Aucune image trouvee dans le fichier")
                return {"success": False, "message": "Aucun code-barres/QR code trouve dans le fichier Excel"}

            print(f"[SUCCESS] {len(worksheet._images)} codes trouves dans le fichier")
            workbook.close()

            # Retourner l'action pour afficher la modal de choix
            print("[SUCCESS] Demande d'affichage de la modal de choix...")
            return {"success": True, "action": "show_choice_modal", "message": "Codes trouvés, affichage des options"}

        except Exception as e:
            print(f"[ERROR] Erreur verification: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def generate_renault_tickets_with_colors(self, selected_colors):
        """Generer les tickets Renault dans les couleurs selectionnees"""
        try:
            if not selected_colors:
                return {"success": False, "message": "Aucune couleur selectionnee"}

            # Vérifier qu'un fichier Excel existe
            if not hasattr(self, 'last_generated_file') or not self.last_generated_file:
                return {"success": False, "message": "Aucun fichier Excel généré trouvé"}

            if not os.path.exists(self.last_generated_file):
                return {"success": False, "message": "Fichier Excel non trouvé"}

            # Obtenir le nom de base du fichier Excel et son répertoire
            excel_dir = os.path.dirname(self.last_generated_file)
            excel_base_name = os.path.splitext(os.path.basename(self.last_generated_file))[0]

            # Créer le nom du dossier principal : même nom que Excel + "_Tickets_Renault"
            main_folder_name = f"{excel_base_name}_Tickets_Renault"
            full_main_folder = os.path.join(excel_dir, main_folder_name)

            print(f"Création dossier tickets: {full_main_folder}")
            os.makedirs(full_main_folder, exist_ok=True)

            results = []

            # Generer pour chaque couleur selectionnee
            for color in selected_colors:
                # Creer sous-dossier pour cette couleur
                color_folder = os.path.join(full_main_folder, f"Tickets_Fond_{color.title()}")
                os.makedirs(color_folder, exist_ok=True)

                # Generer les tickets avec cette couleur
                result = self.generate_renault_etiquettes_with_choice(
                    include_barcodes=True,
                    include_qr=True,
                    background_color=color,
                    output_folder=color_folder
                )

                if result and result.get("success"):
                    results.append(f"Tickets fond {color} generes")
                else:
                    results.append(f"Erreur tickets fond {color}")

            # Ouvrir le dossier principal
            try:
                os.startfile(full_main_folder)
            except Exception as e:
                pass

            # Preparer le message de succes
            color_names = []
            for color in selected_colors:
                color_names.append("noir" if color == "black" else "blanc")

            success_message = f"Tickets generes avec fond {' et '.join(color_names)}"

            return {
                "success": True,
                "message": success_message,
                "folder": full_main_folder,
                "colors_generated": selected_colors
            }

        except Exception as e:
            return {"success": False, "message": "Erreur generation tickets"}

    def generate_renault_etiquettes_with_choice(self, include_barcodes=True, include_qr=True, background_color="black", output_folder=None):
        """PRENDRE les codes du fichier Excel et créer étiquettes photo"""
        try:
            print(f"CREATION ETIQUETTES - Codes-barres: {include_barcodes}, QR: {include_qr}, Fond: {background_color}")

            if not include_barcodes and not include_qr:
                return {"success": False, "message": "Selectionnez au moins un type de code"}

            # Import modules
            openpyxl_module = get_openpyxl()
            PIL_module = get_PIL()
            if not openpyxl_module or not PIL_module:
                return {"success": False, "message": "Erreur chargement modules"}

            # Ouvrir le fichier Excel avec les codes
            print(f"Ouverture fichier: {self.last_generated_file}")
            workbook = openpyxl_module['load_workbook'](self.last_generated_file)
            worksheet = workbook.active

            # Créer dossier de sortie
            if output_folder:
                etiquettes_folder = output_folder
            else:
                base_name = os.path.splitext(os.path.basename(self.last_generated_file))[0]
                etiquettes_folder = os.path.join(self.output_folder, f"{base_name}_Etiquettes_Renault")
            os.makedirs(etiquettes_folder, exist_ok=True)
            print(f"Dossier cree: {etiquettes_folder}")

            # EXTRAIRE LES IMAGES DU FICHIER EXCEL
            extracted_codes = []
            print(f"Extraction de {len(worksheet._images)} images...")

            for i, image in enumerate(worksheet._images):
                try:
                    # Récupérer l'image
                    image_data = image._data()
                    pil_img = PIL_module.open(io.BytesIO(image_data))
                    width, height = pil_img.size

                    # Déterminer le type par dimensions
                    if width > height:  # Code-barres
                        code_type = "barcode"
                        if not include_barcodes:
                            continue
                    else:  # QR code
                        code_type = "qr"
                        if not include_qr:
                            continue

                    extracted_codes.append({
                        'image': pil_img,
                        'type': code_type,
                        'index': i + 1
                    })
                    print(f"Code {code_type} #{i+1} extrait ({width}x{height})")

                except Exception as e:
                    print(f"Erreur extraction image {i+1}: {e}")

            # Fermer le workbook et forcer la libération des ressources
            workbook.close()
            import gc
            gc.collect()

            if not extracted_codes:
                return {"success": False, "message": "Aucun code trouve correspondant a votre selection"}

            # CRÉER LES ÉTIQUETTES PHOTO
            success_count = 0
            total_codes = len(extracted_codes)
            print(f"Creation de {total_codes} etiquettes...")

            # SEULEMENT QR CODES - ligne par ligne (codes-barres limites a 132 caracteres)
            qrcodes = [code for code in extracted_codes if code['type'] == 'qr']

            print(f"Generation de {len(qrcodes)} QR codes SEULEMENT (ligne par ligne)")

            # CHAQUE QR CODE prend SA ligne Excel correspondante
            for i, code_data in enumerate(qrcodes):
                try:
                    ticket_number = i + 1      # QR #1, #2, #3, #4, #5
                    excel_row_index = i        # Ligne Excel 0, 1, 2, 3, 4

                    print(f"=== QR CODE #{ticket_number} ===")
                    print(f"Ligne Excel {excel_row_index + 1} -> QR code #{ticket_number}")

                    # Prendre les 2 premieres colonnes de la ligne Excel
                    row_data = self._get_excel_row_data(excel_row_index)
                    print(f"Donnees Excel: {row_data}")

                    # Creer l'etiquette QR avec les donnees Excel
                    etiquette_img = self._create_renault_photo_etiquette(
                        code_data['image'],
                        'qr',
                        ticket_number,
                        PIL_module,
                        row_data,
                        background_color
                    )

                    if etiquette_img:
                        # Sauvegarder QR code avec le bon nom
                        filename = f"Etiquette_QR_{ticket_number:03d}.png"
                        filepath = os.path.join(etiquettes_folder, filename)
                        etiquette_img.save(filepath, 'PNG', dpi=(300, 300))
                        success_count += 1
                        print(f"OK QR code sauvegarde: {filename}")

                except Exception as e:
                    print(f"ERREUR QR code #{i+1}: {e}")

                    if etiquette_img:
                        # Sauvegarder avec le bon numéro de ticket
                        filename = f"Etiquette_{code_data['type'].upper()}_{ticket_number:03d}.png"
                        filepath = os.path.join(etiquettes_folder, filename)
                        etiquette_img.save(filepath, 'PNG', dpi=(300, 300))
                        success_count += 1
                        print(f"OK Etiquette sauvegardee: {filename}")
                        print(f"Etiquette creee: {filename}")

                    # Progression en temps réel (seulement le pourcentage)
                    progress = int((i + 1) / total_codes * 100)
                    self.update_progress_callback(progress)

                except Exception as e:
                    print(f"Erreur etiquette {i+1}: {e}")

            return {
                "success": True,
                "message": f"{success_count} etiquettes Renault creees !\nDossier: {etiquettes_folder}\nFormat: PNG 300 DPI (7cm x 4cm)",
                "folder": etiquettes_folder,
                "success_count": success_count
            }

        except Exception as e:
            print(f"ERREUR: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def _get_excel_row_data(self, row_index):
        """Récupérer les données des 2 premières colonnes Excel pour une ligne donnée"""
        try:
            # Import pandas
            pandas_module = get_pandas()
            if not pandas_module:
                print(f"ERREUR: Pandas non disponible pour ligne {row_index}")
                return ["", ""]

            # Lire le fichier Excel original (pas le fichier généré) avec gestion des ressources
            with open(self.input_file, 'rb') as file:
                df = pandas_module.read_excel(file)
            print(f"DEBUG: Lecture Excel pour ligne {row_index}, total lignes: {len(df)}")

            # Forcer la libération des ressources
            import gc
            gc.collect()

            if row_index < len(df):
                row = df.iloc[row_index]
                print(f"DEBUG: Ligne {row_index} trouvée dans Excel: {row.tolist()}")

                # Prendre les 2 premières colonnes avec debug détaillé
                col1_raw = row.iloc[0] if len(row) > 0 else None
                col2_raw = row.iloc[1] if len(row) > 1 else None

                print(f"DEBUG: Colonne 1 brute: '{col1_raw}' (type: {type(col1_raw)})")
                print(f"DEBUG: Colonne 2 brute: '{col2_raw}' (type: {type(col2_raw)})")

                col1 = str(col1_raw) if col1_raw is not None and not pandas_module.isna(col1_raw) else ""
                col2 = str(col2_raw) if col2_raw is not None and not pandas_module.isna(col2_raw) else ""

                print(f"DEBUG: Données Excel finales ligne {row_index}: ['{col1}', '{col2}']")
                return [col1, col2]
            else:
                print(f"ATTENTION: QR #{row_index + 1} - Ligne Excel {row_index} n'existe pas (fichier a seulement {len(df)} lignes)")
                print(f"Solution: Ajouter plus de lignes dans Excel ou utiliser un fichier avec plus de données")
                # Retourner des données vides pour forcer l'utilisation du texte par défaut
                return ["", ""]

        except Exception as e:
            print(f"ERREUR lecture donnees Excel ligne {row_index}: {e}")
            import traceback
            traceback.print_exc()
            return ["", ""]

    def _create_renault_photo_etiquette(self, code_img, code_type, index, PIL_module, row_data=None, background_color="black"):
        """Créer une étiquette Renault format photo avec logo et code"""
        try:
            # Dimensions étiquette VERTICALE : 3.9cm x 7cm à 300 DPI
            etiquette_width = int(3.9 * 300 / 2.54)   # 461 pixels (largeur)
            etiquette_height = int(7 * 300 / 2.54)    # 827 pixels (hauteur)

            # Créer l'image de base avec la couleur de fond choisie
            bg_color = 'white' if background_color == 'white' else 'black'
            etiquette = PIL_module.new('RGB', (etiquette_width, etiquette_height), color=bg_color)

            # Ajouter cadre orange Renault (bordure fine)
            from PIL import ImageDraw
            draw = ImageDraw.Draw(etiquette)
            renault_orange = '#FF6600'  # Couleur orange Renault
            border_width = 3  # Bordure fine

            # Dessiner le cadre orange autour de l'étiquette
            for i in range(border_width):
                draw.rectangle([i, i, etiquette_width-1-i, etiquette_height-1-i],
                             outline=renault_orange, width=1)

            # Remplacer le logo par un nom de grande taille + description
            # Taille uniforme pour tous les tickets (noir et blanc)
            nom_font_size = 80  # Taille agrandie pour le nom principal

            # Position à 0,8cm du haut du ticket
            position_haut_cm = 0.8
            position_haut_pixels = int(position_haut_cm * 300 / 2.54)  # Convertir 0,8cm en pixels

            print(f"Configuration nom: taille {nom_font_size}pt, position {position_haut_pixels}px du haut")

            # Position du logo sera définie plus tard selon le type de code

            # Position et zone pour le code
            from PIL import ImageDraw
            draw = ImageDraw.Draw(etiquette)

            if code_type == "barcode":
                # Code-barres : disposition classique avec nom centré en haut
                code_width = int(4.4 * 300 / 2.54)   # 520 pixels (4.4cm)
                code_height = int(1.5 * 300 / 2.54)  # 177 pixels (1.5cm)
                code_x = (etiquette_width - code_width) // 2

                # Dessiner le nom de grande taille centré en haut pour les codes-barres
                try:
                    from PIL import ImageFont
                    # Utiliser une police de grande taille pour remplacer le logo
                    try:
                        # Essayer d'utiliser une police en gras pour le nom principal
                        nom_font = ImageFont.truetype("arialbd.ttf", nom_font_size)
                        print(f"Police nom utilisée: Arial Bold {nom_font_size}pt")
                    except:
                        try:
                            nom_font = ImageFont.truetype("arial.ttf", nom_font_size)
                            print(f"Police nom utilisée: Arial {nom_font_size}pt")
                        except:
                            nom_font = ImageFont.load_default()
                            print("Police nom utilisée: Police par défaut")

                    # Texte du nom à afficher
                    nom_text = "SOMACA"

                    # Calculer la position du nom (centré horizontalement)
                    nom_bbox = draw.textbbox((0, 0), nom_text, font=nom_font)
                    nom_text_width = nom_bbox[2] - nom_bbox[0]
                    nom_text_height = nom_bbox[3] - nom_bbox[1]
                    nom_x = (etiquette_width - nom_text_width) // 2

                    # Position à 0,8cm du haut du ticket
                    nom_y = position_haut_pixels

                    # Couleur du nom selon la couleur de fond
                    nom_color = 'black' if background_color == 'white' else 'white'

                    # Dessiner le nom
                    draw.text((nom_x, nom_y), nom_text, fill=nom_color, font=nom_font)
                    print(f"Nom '{nom_text}' ajouté à position ({nom_x}, {nom_y}) couleur {nom_color}")



                    # Position du code-barres sous le nom avec espace de 0,8cm
                    espace_nom_code_cm = 0.8
                    espace_nom_code_pixels = int(espace_nom_code_cm * 300 / 2.54)  # Convertir 0,8cm en pixels
                    code_y = nom_y + nom_text_height + espace_nom_code_pixels

                except Exception as e:
                    print(f"Erreur ajout nom: {e}")
                    # Si pas de nom, position par défaut
                    code_y = 120

                # Créer zone pour le code-barres (contraste avec le fond)
                barcode_zone_color = 'black' if background_color == 'white' else 'white'
                draw.rectangle([code_x, code_y, code_x + code_width, code_y + code_height], fill=barcode_zone_color)

            else:  # QR code - disposition verticale comme la nouvelle photo
                # Disposition verticale : Nom en haut, QR au milieu, texte en bas
                qr_width = int(3.0 * 300 / 2.54)     # 354 pixels (3.0cm)
                qr_height = int(3.0 * 300 / 2.54)    # 354 pixels (3.0cm)

                # Dessiner le nom de grande taille centré en haut pour les QR codes
                try:
                    from PIL import ImageFont
                    # Utiliser une police de grande taille pour remplacer le logo
                    try:
                        # Essayer d'utiliser une police en gras pour le nom principal
                        nom_font = ImageFont.truetype("arialbd.ttf", nom_font_size)
                        print(f"Police nom utilisée: Arial Bold {nom_font_size}pt")
                    except:
                        try:
                            nom_font = ImageFont.truetype("arial.ttf", nom_font_size)
                            print(f"Police nom utilisée: Arial {nom_font_size}pt")
                        except:
                            nom_font = ImageFont.load_default()
                            print("Police nom utilisée: Police par défaut")

                    # Texte du nom à afficher
                    nom_text = "SOMACA"

                    # Calculer la position du nom (centré horizontalement)
                    nom_bbox = draw.textbbox((0, 0), nom_text, font=nom_font)
                    nom_text_width = nom_bbox[2] - nom_bbox[0]
                    nom_text_height = nom_bbox[3] - nom_bbox[1]
                    nom_x = (etiquette_width - nom_text_width) // 2

                    # Position à 0,5cm du haut du ticket
                    nom_y = position_haut_pixels

                    # Couleur du nom selon la couleur de fond
                    nom_color = 'black' if background_color == 'white' else 'white'

                    # Dessiner le nom
                    draw.text((nom_x, nom_y), nom_text, fill=nom_color, font=nom_font)
                    print(f"Nom '{nom_text}' ajouté à position ({nom_x}, {nom_y}) couleur {nom_color}")



                    # Position QR code centré horizontalement
                    # Position X : centré sur l'étiquette
                    code_x = (etiquette_width - qr_width) // 2
                    # Position Y : sous le nom avec espace de 0,8cm
                    espace_nom_qr_cm = 0.8
                    espace_nom_qr_pixels = int(espace_nom_qr_cm * 300 / 2.54)  # Convertir 0,8cm en pixels
                    code_y = nom_y + nom_text_height + espace_nom_qr_pixels

                    # Debug position QR code
                    distance_gauche = code_x
                    distance_droite = etiquette_width - (code_x + qr_width)
                    print(f"QR Code - Centré horizontalement")
                    print(f"QR Code - Largeur étiquette: {etiquette_width}px, QR largeur: {qr_width}px")
                    print(f"QR Code - Position X: {code_x}px, Distance gauche: {distance_gauche}px, Distance droite: {distance_droite}px")

                except Exception as e:
                    print(f"Erreur ajout nom: {e}")
                    # Position QR code centré au milieu du ticket si pas de nom
                    code_x = (etiquette_width - qr_width) // 2
                    code_y = (etiquette_height - qr_height) // 2

                # Pas de zone blanche pour QR code - directement sur fond noir

            # Pour les codes-barres : découpage et redimensionnement
            if code_type == "barcode":
                # Le code-barres dans Excel fait 4.4cm x 2.2cm
                # Zone blanche : 4.4cm x 1.5cm
                # On doit couper de 2.2cm à 1.5cm (enlever 0.7cm = 32% du bas)

                # Couper les 32% du bas pour enlever le texte et ajuster la hauteur
                crop_height = int(code_img.height * 0.68)  # Garder 68% du haut
                code_img_cropped = code_img.crop((0, 0, code_img.width, crop_height))

                # Dimensions finales pour l'étiquette : 4.4cm x 1.5cm (remplir toute la zone blanche)
                final_width = int(4.4 * 300 / 2.54)  # 4.4cm = 520 pixels à 300 DPI
                final_height = int(1.5 * 300 / 2.54) # 1.5cm = 177 pixels à 300 DPI

                # Redimensionner le code-barres coupé pour remplir toute la zone blanche
                code_resized = code_img_cropped.resize((final_width, final_height), PIL_module.Resampling.LANCZOS if hasattr(PIL_module, 'Resampling') else PIL_module.LANCZOS)

                # Coller directement dans la zone blanche (sans marge car même taille)
                etiquette.paste(code_resized, (code_x, code_y))

                print(f"Code-barres coupe et redimensionne: {code_img.width}x{code_img.height} -> {final_width}x{final_height}")

            else:  # QR code - avec fond blanc si fond noir
                # Le QR code dans Excel fait 3.0cm × 3.0cm
                # Dimensions finales du QR code : exactement 3.0cm × 3.0cm
                qr_final_width = int(3.0 * 300 / 2.54)   # 3.0cm = 354 pixels à 300 DPI
                qr_final_height = int(3.0 * 300 / 2.54)  # 3.0cm = 354 pixels à 300 DPI

                # Si fond noir, créer une zone blanche autour du QR code pour la lisibilité
                if background_color == "black":
                    # Créer une zone blanche plus grande que le QR code
                    white_zone_margin = 15  # Marge blanche autour du QR code
                    white_zone_width = qr_final_width + (white_zone_margin * 2)
                    white_zone_height = qr_final_height + (white_zone_margin * 2)

                    # Position de la zone blanche (centrée où devrait être le QR code)
                    white_zone_x = code_x - white_zone_margin
                    white_zone_y = code_y - white_zone_margin

                    # Dessiner la zone blanche
                    from PIL import ImageDraw
                    draw = ImageDraw.Draw(etiquette)
                    draw.rectangle([white_zone_x, white_zone_y,
                                  white_zone_x + white_zone_width,
                                  white_zone_y + white_zone_height],
                                 fill='white')

                    print(f"Zone blanche créée: {white_zone_width}x{white_zone_height} pixels avec marge {white_zone_margin}px")

                # Redimensionner le QR code à sa taille finale exacte
                code_resized = code_img.resize((qr_final_width, qr_final_height), PIL_module.Resampling.LANCZOS if hasattr(PIL_module, 'Resampling') else PIL_module.LANCZOS)

                # Coller le QR code (sur fond blanc si fond noir, directement si fond blanc)
                etiquette.paste(code_resized, (code_x, code_y))

                if background_color == "black":
                    print(f"QR code 3.0cm sur zone blanche (fond noir): {code_img.width}x{code_img.height} -> {qr_final_width}x{qr_final_height}")
                else:
                    print(f"QR code 3.0cm directement sur fond blanc: {code_img.width}x{code_img.height} -> {qr_final_width}x{qr_final_height}")

            # Ajouter texte descriptif directement sous le code-barres/QR code
            try:
                from PIL import ImageFont

                # DEBUG: Afficher les données reçues
                print(f"DEBUG TEXTE: row_data reçu pour ticket #{index}: {row_data}")

                # Utiliser les données Excel avec vérification robuste et debug
                text = ""
                if row_data and len(row_data) >= 1:
                    # Nettoyer et vérifier les données avec debug détaillé
                    col1_raw = row_data[0] if row_data[0] is not None else ""
                    col2_raw = row_data[1] if len(row_data) >= 2 and row_data[1] is not None else ""

                    col1 = str(col1_raw).strip() if col1_raw and str(col1_raw).strip() != "nan" else ""
                    col2 = str(col2_raw).strip() if col2_raw and str(col2_raw).strip() != "nan" else ""

                    print(f"DEBUG TEXTE: col1='{col1}', col2='{col2}'")

                    if col1 and col2:
                        # Les deux colonnes ont des données
                        text = f"{col1} - {col2}"
                        print(f"DEBUG TEXTE: Utilisation des deux colonnes: '{text}'")
                    elif col1:
                        # Seulement la première colonne
                        text = col1
                        print(f"DEBUG TEXTE: Utilisation colonne 1 seulement: '{text}'")
                    elif col2:
                        # Seulement la deuxième colonne
                        text = col2
                        print(f"DEBUG TEXTE: Utilisation colonne 2 seulement: '{text}'")
                else:
                    print(f"DEBUG TEXTE: Aucune donnée row_data valide")

                # Si aucun texte valide trouvé, utiliser un texte par défaut
                if not text:
                    text = f"{code_type.upper()} #{index:03d}"
                    print(f"DEBUG TEXTE: Utilisation texte par défaut: '{text}'")

                print(f"TEXTE FINAL pour ticket #{index}: '{text}'")

                try:
                    # Police Roboto Mono pour le texte principal - 36pt GRAS
                    font = ImageFont.truetype("RobotoMono-Bold.ttf", 36)  # Roboto Mono Bold 36pt
                    print("Police utilisée: Roboto Mono Bold 36pt")
                except:
                    try:
                        # Fallback : Roboto Mono normal - 36pt
                        font = ImageFont.truetype("RobotoMono-Regular.ttf", 36)
                        print("Police utilisée: Roboto Mono Regular 36pt")
                    except:
                        try:
                            # Fallback : Arial Bold si Roboto Mono pas disponible - 36pt
                            font = ImageFont.truetype("arialbd.ttf", 36)
                            print("Police utilisée: Arial Bold 36pt (fallback)")
                        except:
                            try:
                                # Fallback : Arial normal - 36pt
                                font = ImageFont.truetype("arial.ttf", 36)
                                print("Police utilisée: Arial Normal 36pt (fallback)")
                            except:
                                # Fallback final : police par défaut
                                font = ImageFont.load_default()
                                print("Police utilisée: Police par défaut (fallback)")

                # Calculer la position du texte (centré, directement sous le code)
                text_bbox = draw.textbbox((0, 0), text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_x = (etiquette_width - text_width) // 2

                # Position du texte selon le type de code
                if code_type == "barcode":
                    text_y = code_y + code_height + 10  # 10 pixels sous la zone blanche
                else:  # QR code - texte à 1,1cm du bas de l'étiquette
                    distance_bas_cm = 1.1  # 1,1cm du bas
                    distance_bas_pixels = int(distance_bas_cm * 300 / 2.54)  # Convertir 1,1cm en pixels (130 pixels)
                    text_y = etiquette_height - distance_bas_pixels  # 1,1cm du bas de l'étiquette
                    print(f"Position texte: Y={text_y}, hauteur étiquette={etiquette_height}, distance bas={distance_bas_pixels}px (1,1cm)")

                # Couleur du texte selon la couleur de fond
                text_color = 'black' if background_color == 'white' else 'white'
                draw.text((text_x, text_y), text, fill=text_color, font=font)
                print(f"Texte ajoute sous le code: '{text}' (couleur: {text_color})")

            except Exception as text_error:
                print(f"Erreur ajout texte: {text_error}")

            return etiquette

        except Exception as e:
            print(f"Erreur creation etiquette photo: {e}")
            return None

    def _remove_text_from_barcode(self, barcode_img, PIL_module):
        """Supprimer intelligemment le texte d'un code-barres en gardant seulement les barres"""
        try:
            # Convertir en niveaux de gris pour l'analyse
            gray_img = barcode_img.convert('L')
            width, height = gray_img.size

            # Analyser ligne par ligne pour trouver où commencent les barres
            pixels = gray_img.load()

            # Trouver la première ligne qui contient des barres (variations noir/blanc)
            first_bar_line = 0
            for y in range(height):
                # Compter les transitions noir/blanc sur cette ligne
                transitions = 0
                prev_pixel = pixels[0, y]

                for x in range(1, width):
                    current_pixel = pixels[x, y]
                    # Si changement significatif de couleur (noir <-> blanc)
                    if abs(current_pixel - prev_pixel) > 100:
                        transitions += 1
                    prev_pixel = current_pixel

                # Si plus de 10 transitions, c'est probablement une ligne de barres
                if transitions > 10:
                    first_bar_line = y
                    break

            # Trouver la dernière ligne qui contient des barres
            last_bar_line = height - 1
            for y in range(height - 1, -1, -1):
                transitions = 0
                prev_pixel = pixels[0, y]

                for x in range(1, width):
                    current_pixel = pixels[x, y]
                    if abs(current_pixel - prev_pixel) > 100:
                        transitions += 1
                    prev_pixel = current_pixel

                if transitions > 10:
                    last_bar_line = y
                    break

            # Ajouter une petite marge de sécurité
            margin = 5
            crop_top = max(0, first_bar_line - margin)
            crop_bottom = min(height, last_bar_line + margin)

            # Couper l'image pour garder seulement les barres
            cropped_img = barcode_img.crop((0, crop_top, width, crop_bottom))

            print(f"Code-barres nettoye: {width}x{height} -> {width}x{crop_bottom-crop_top}")
            return cropped_img

        except Exception as e:
            print(f"Erreur nettoyage code-barres: {e}")
            # En cas d'erreur, retourner l'image originale coupée de 30%
            crop_height = int(barcode_img.height * 0.7)
            return barcode_img.crop((0, 0, barcode_img.width, crop_height))

# Instance globale du générateur
generator = SomacaBarcodeGenerator()

class Api:
    def __init__(self):
        """Initialiser l'API"""
        print("Initialisation de l'API...")

    def select_file(self):
        """Sélectionner un fichier Excel - Version ULTRA-RAPIDE optimisée"""
        try:
            print("Debut selection fichier OPTIMISÉ...")

            result = {"file_path": None, "error": None, "completed": False}

            def open_dialog():
                try:
                    print("Creation dialogue Tkinter RAPIDE...")

                    # Import différé optimisé
                    tkinter_module = get_tkinter()
                    if not tkinter_module:
                        result["error"] = "Erreur chargement tkinter"
                        result["completed"] = True
                        return

                    # Créer une nouvelle instance Tkinter
                    root = tkinter_module['tk'].Tk()
                    root.withdraw()  # Cacher la fenêtre principale
                    
                    # Forcer au premier plan
                    root.attributes('-topmost', True)
                    root.lift()
                    root.focus_force()
                    
                    # Petit délai pour s'assurer que la fenêtre est prête
                    time.sleep(0.1)
                    
                    print("Ouverture dialogue fichier...")
                    file_path = tkinter_module['filedialog'].askopenfilename(
                        title="Sélectionner un fichier Excel",
                        filetypes=[
                            ("Fichiers Excel", "*.xlsx *.xls"),
                            ("Tous les fichiers", "*.*")
                        ],
                        parent=root
                    )
                    
                    print(f"Fichier retourne: {file_path}")
                    result["file_path"] = file_path
                    result["completed"] = True
                    
                    # Nettoyer proprement
                    root.quit()
                    root.destroy()
                    
                except Exception as e:
                    print(f"Erreur dans dialogue: {e}")
                    result["error"] = str(e)
                    result["completed"] = True
            
            # Lancer dans un thread séparé
            print("Lancement thread dialogue...")
            thread = threading.Thread(target=open_dialog)
            thread.daemon = True
            thread.start()
            
            # Attendre avec timeout
            thread.join(timeout=60)  # 60 secondes max
            
            # Vérifier si le thread s'est terminé
            if thread.is_alive():
                print("Timeout du dialogue")
                return {"success": False, "message": "Timeout lors de la sélection du fichier"}
            
            if result["error"]:
                print(f"Erreur: {result['error']}")
                return {"success": False, "message": f"Erreur: {result['error']}"}
            
            if result["file_path"]:
                print(f"Fichier selectionne: {result['file_path']}")
                # Définir le fichier dans le générateur
                set_result = generator.set_input_file(result["file_path"])
                if set_result["success"]:
                    return {"success": True, "message": f"Fichier sélectionné: {result['file_path']}"}
                else:
                    return set_result
            else:
                print("Aucun fichier selectionne")
                return {"success": False, "message": "Aucun fichier sélectionné"}
                
        except Exception as e:
            print(f"Erreur selection fichier: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def select_folder(self):
        """Sélectionner un dossier de destination - Version Tkinter robuste"""
        try:
            print("Debut selection dossier...")
            import threading
            import time

            result = {"folder_path": None, "error": None, "completed": False}

            def open_dialog():
                try:
                    print("Creation dialogue dossier Tkinter...")

                    # Import différé optimisé
                    tkinter_module = get_tkinter()
                    if not tkinter_module:
                        result["error"] = "Erreur chargement tkinter"
                        result["completed"] = True
                        return

                    # Créer une nouvelle instance Tkinter
                    root = tkinter_module['tk'].Tk()
                    root.withdraw()  # Cacher la fenêtre principale

                    # Forcer au premier plan
                    root.attributes('-topmost', True)
                    root.lift()
                    root.focus_force()

                    # Petit délai pour s'assurer que la fenêtre est prête
                    time.sleep(0.1)

                    print("Ouverture dialogue dossier...")
                    folder_path = tkinter_module['filedialog'].askdirectory(
                        title="Sélectionner le dossier de destination",
                        parent=root
                    )

                    print(f"Dossier retourne: {folder_path}")
                    result["folder_path"] = folder_path
                    result["completed"] = True

                    # Nettoyer proprement
                    root.quit()
                    root.destroy()

                except Exception as e:
                    print(f"Erreur dans dialogue: {e}")
                    result["error"] = str(e)
                    result["completed"] = True

            # Lancer dans un thread séparé
            print("Lancement thread dialogue...")
            thread = threading.Thread(target=open_dialog)
            thread.daemon = True
            thread.start()

            # Attendre avec timeout
            thread.join(timeout=60)  # 60 secondes max

            # Vérifier si le thread s'est terminé
            if thread.is_alive():
                print("Timeout du dialogue")
                return {"success": False, "message": "Timeout lors de la sélection du dossier"}

            if result["error"]:
                print(f"Erreur: {result['error']}")
                return {"success": False, "message": f"Erreur: {result['error']}"}

            if result["folder_path"]:
                print(f"Dossier selectionne: {result['folder_path']}")
                # Définir le dossier dans le générateur
                set_result = generator.set_output_folder(result["folder_path"])
                if set_result["success"]:
                    return {"success": True, "message": f"Dossier sélectionné: {result['folder_path']}"}
                else:
                    return set_result
            else:
                print("Aucun dossier selectionne")
                return {"success": False, "message": "Aucun dossier sélectionné"}

        except Exception as e:
            print(f"Erreur selection dossier: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def generate_codes(self, generate_barcodes=True, generate_qr=True):
        """Générer les codes-barres et QR codes"""
        return generator.generate_codes(generate_barcodes, generate_qr)

    def start_generation(self, generate_barcodes=True, generate_qr=True):
        """Démarrer la génération - Version synchrone pour webview"""
        try:
            # Réinitialiser l'état d'annulation
            generator.is_cancelled = False

            print(f"Démarrage génération - Codes-barres: {generate_barcodes}, QR: {generate_qr}")
            print(f"Fichier d'entrée: {generator.input_file}")
            print(f"Dossier de sortie: {generator.output_folder}")

            # Vérifications préliminaires
            if not generator.input_file or not generator.output_folder:
                return {"success": False, "message": "Veuillez sélectionner un fichier et un dossier de destination"}

            if not os.path.exists(generator.input_file):
                return {"success": False, "message": f"Fichier non trouvé: {generator.input_file}"}

            if not os.path.exists(generator.output_folder):
                return {"success": False, "message": f"Dossier non trouvé: {generator.output_folder}"}

            # Exécuter la génération directement
            result = self.generate_codes(generate_barcodes, generate_qr)
            print(f"Résultat: {result}")

            # Vérifier si l'opération a été annulée
            if generator.is_cancelled:
                result = {"success": False, "message": "Opération annulée par l'utilisateur"}

            return result

        except Exception as e:
            print(f"Erreur dans la génération: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def cancel_current_operation(self):
        """Annuler l'opération en cours"""
        return generator.cancel_operation()

    def generate_framed_images(self):
        """Générer des images encadrées 6cm x 3cm avec logo Renault"""
        try:
            if not generator.input_file or not generator.output_folder:
                return {"success": False, "message": "Veuillez sélectionner un fichier et un dossier de destination"}

            if not os.path.exists(generator.input_file):
                return {"success": False, "message": f"Fichier non trouvé: {generator.input_file}"}

            if not os.path.exists(generator.output_folder):
                return {"success": False, "message": f"Dossier non trouvé: {generator.output_folder}"}

            # Import différé pandas
            pandas_module = get_pandas()
            if not pandas_module:
                return {"success": False, "message": "Erreur chargement pandas"}

            # Lire le fichier Excel avec gestion des ressources
            try:
                with open(generator.input_file, 'rb') as file:
                    df = pandas_module.read_excel(file)
                # Forcer la libération des ressources
                import gc
                gc.collect()
            except Exception as e:
                return {"success": False, "message": f"Erreur lecture fichier Excel: {str(e)}"}

            if df.empty:
                return {"success": False, "message": "Le fichier Excel est vide"}

            # Convertir en données
            data = df.values.tolist()

            # Créer le dossier pour les images encadrées
            base_name = os.path.splitext(os.path.basename(generator.input_file))[0]
            images_folder = os.path.join(generator.output_folder, f"{base_name}_Images_Encadrees")
            os.makedirs(images_folder, exist_ok=True)

            # Générer les images encadrées
            generated_count = 0
            total_rows = len(data)

            for row_index, row_data in enumerate(data):
                if generator.is_cancelled:
                    return {"success": False, "message": "Opération annulée par l'utilisateur"}

                try:
                    # Générer l'image encadrée PIL directement
                    framed_pil_img = generator.generate_framed_pil_image(row_data, row_index + 1)

                    if framed_pil_img:
                        # Sauvegarder l'image
                        filename = f"Image_Encadree_{row_index + 1:03d}.png"
                        filepath = os.path.join(images_folder, filename)

                        # Sauvegarder l'image PIL avec haute résolution
                        framed_pil_img.save(filepath, 'PNG', dpi=(300, 300))

                        generated_count += 1

                    # Mise à jour progression
                    progress = int((row_index + 1) / total_rows * 100)
                    generator.update_progress_callback(progress)

                except Exception as e:
                    print(f"Erreur génération image ligne {row_index + 1}: {e}")
                    continue

            return {
                "success": True,
                "message": f"Images encadrées générées avec succès !\n{generated_count} images créées dans:\n{images_folder}",
                "generated_count": generated_count,
                "folder": images_folder
            }

        except Exception as e:
            print(f"Erreur génération images encadrées: {e}")
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def export_images_for_print(self, export_barcodes=True, export_qr=True):
        """Exporter les images haute résolution pour impression selon les choix utilisateur"""
        try:
            # Utiliser l'instance globale generator pour la compatibilité
            if not hasattr(generator, 'last_generated_file') or not generator.last_generated_file:
                return {"success": False, "message": "Aucun fichier Excel généré à extraire"}

            if not os.path.exists(generator.last_generated_file):
                return {"success": False, "message": "Le fichier Excel généré n'existe plus"}

            print(f"Export des images depuis: {generator.last_generated_file}")

            # Créer le dossier d'export
            base_name = os.path.splitext(os.path.basename(generator.last_generated_file))[0]
            export_base_folder = os.path.join(generator.output_folder, f"{base_name}_Images_HR")

            # Créer les sous-dossiers selon les choix
            barcode_folder = None
            qr_folder = None

            if export_barcodes:
                barcode_folder = os.path.join(export_base_folder, "Codes_Barres")
                os.makedirs(barcode_folder, exist_ok=True)
                print(f"Dossier codes-barres: {barcode_folder}")

            if export_qr:
                qr_folder = os.path.join(export_base_folder, "QR_Codes")
                os.makedirs(qr_folder, exist_ok=True)
                print(f"Dossier QR codes: {qr_folder}")

            if not export_barcodes and not export_qr:
                return {"success": False, "message": "Aucun type d'image sélectionné pour l'export"}

            # Ouvrir le fichier Excel et extraire les images
            from openpyxl import load_workbook
            workbook = load_workbook(generator.last_generated_file)
            worksheet = workbook.active

            exported_count = 0
            barcode_count = 0
            qr_count = 0

            # Parcourir toutes les images dans la feuille
            for image in worksheet._images:
                try:
                    # Extraire les données de l'image
                    image_data = image._data()

                    # Déterminer le type d'image basé sur ses dimensions
                    from PIL import Image
                    pil_img = Image.open(io.BytesIO(image_data))
                    width, height = pil_img.size

                    # Déterminer le type basé sur les dimensions
                    if width > height:  # Code-barres (plus large que haut)
                        if export_barcodes and barcode_folder:
                            filename = f"barcode_{barcode_count + 1:04d}.png"
                            save_path = os.path.join(barcode_folder, filename)

                            # Redimensionner en haute résolution pour impression
                            # 3,5cm x 1,5cm à 300 DPI = 413x177 pixels
                            pil_img_resized = pil_img.resize((413, 177), Image.Resampling.LANCZOS)
                            pil_img_resized.save(save_path, 'PNG', dpi=(300, 300))

                            print(f"Code-barres extrait: {filename}")
                        else:
                            # Code-barres ignoré car non sélectionné
                            continue

                        barcode_count += 1
                    else:  # QR code (carré)
                        if export_qr and qr_folder:
                            filename = f"qr_code_{qr_count + 1:04d}.png"
                            save_path = os.path.join(qr_folder, filename)

                            # Redimensionner en haute résolution pour impression
                            # 2cm x 2cm à 300 DPI = 236x236 pixels
                            pil_img_resized = pil_img.resize((236, 236), Image.Resampling.LANCZOS)
                            pil_img_resized.save(save_path, 'PNG', dpi=(300, 300))

                            print(f"QR code extrait: {filename}")
                        else:
                            # QR code ignoré car non sélectionné
                            continue

                        qr_count += 1

                    exported_count += 1

                    # Mise à jour progression
                    progress = (exported_count / len(worksheet._images)) * 100
                    self.update_progress_callback(int(progress))

                except Exception as img_error:
                    print(f"Erreur extraction image: {img_error}")
                    continue

            workbook.close()

            print(f"Extraction terminée: {barcode_count} codes-barres + {qr_count} QR codes")

            return {
                "success": True,
                "message": f"Extraction terminée ! {barcode_count} codes-barres et {qr_count} QR codes extraits.\nDossier: {export_base_folder}",
                "exported": exported_count,
                "barcodes": barcode_count,
                "qr_codes": qr_count,
                "folder": export_base_folder
            }

        except Exception as e:
            print(f"Erreur lors de l'extraction: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def get_available_printers(self):
        """Obtenir la liste des imprimantes disponibles"""
        try:
            # Import différé win32print
            win32_module = get_win32()
            if not win32_module:
                return {"success": False, "message": "Module win32print non disponible"}

            printers = []

            # Énumérer toutes les imprimantes
            printer_info = win32_module.EnumPrinters(win32_module.PRINTER_ENUM_LOCAL | win32_module.PRINTER_ENUM_CONNECTIONS)
            for printer in printer_info:
                printer_name = printer[2]  # Le nom de l'imprimante
                printers.append(printer_name)

            # Obtenir l'imprimante par défaut
            default_printer = win32_module.GetDefaultPrinter()

            return {
                "success": True,
                "printers": printers,
                "default": default_printer
            }

        except Exception as e:
            print(f"Erreur détection imprimantes: {e}")
            return {
                "success": False,
                "message": f"Erreur détection imprimantes: {str(e)}",
                "printers": [],
                "default": None
            }

    def print_directly(self, export_barcodes=True, export_qr=True):
        """Imprimer directement sur l'imprimante avec disposition optimale A4"""
        try:
            # Utiliser l'instance globale generator pour la compatibilité
            if not hasattr(generator, 'last_generated_file') or not generator.last_generated_file:
                return {"success": False, "message": "Aucun fichier Excel généré à imprimer"}

            if not os.path.exists(generator.last_generated_file):
                return {"success": False, "message": "Le fichier Excel généré n'existe plus"}

            print(f"Impression directe depuis: {generator.last_generated_file}")

            # Vérifier les imprimantes disponibles
            printer_info = self.get_available_printers()
            if not printer_info["success"] or not printer_info["printers"]:
                return {"success": False, "message": "Aucune imprimante détectée sur le système"}

            default_printer = printer_info["default"]
            print(f"Imprimante par défaut: {default_printer}")

            # Extraire les images du fichier Excel
            from openpyxl import load_workbook
            workbook = load_workbook(generator.last_generated_file)
            worksheet = workbook.active

            barcode_images = []
            qr_images = []

            # Import différé PIL
            PIL_module = get_PIL()
            if not PIL_module:
                return {"success": False, "message": "Erreur chargement PIL"}

            # Extraire et redimensionner les images
            for image in worksheet._images:
                try:
                    image_data = image._data()
                    pil_img = PIL_module.open(io.BytesIO(image_data))
                    width, height = pil_img.size

                    # Redimensionner à la résolution d'impression (300 DPI)
                    if width > height:  # Code-barres
                        if export_barcodes:
                            # 3.5cm x 1.5cm à 300 DPI = 413x177 pixels
                            resized_img = pil_img.resize((413, 177), PIL_module.Resampling.LANCZOS)
                            barcode_images.append(resized_img)
                    else:  # QR code
                        if export_qr:
                            # 2cm x 2cm à 300 DPI = 236x236 pixels
                            resized_img = pil_img.resize((236, 236), PIL_module.Resampling.LANCZOS)
                            qr_images.append(resized_img)

                except Exception as img_error:
                    print(f"Erreur traitement image: {img_error}")
                    continue

            workbook.close()

            if not barcode_images and not qr_images:
                return {"success": False, "message": "Aucune image à imprimer trouvée"}

            print(f"Images à imprimer: {len(barcode_images)} codes-barres, {len(qr_images)} QR codes")

            # Créer une disposition optimale A4 pour l'impression
            return self._create_print_layout(barcode_images, qr_images, default_printer)

        except Exception as e:
            print(f"Erreur impression directe: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur impression: {str(e)}"}

    def _create_print_layout(self, barcode_images, qr_images, printer_name):
        """Créer une disposition optimale A4 pour l'impression"""
        try:
            from PIL import Image, ImageDraw

            # Dimensions A4 à 300 DPI
            a4_width = 2480  # 21cm à 300 DPI
            a4_height = 3508  # 29.7cm à 300 DPI

            # Marges (1cm de chaque côté)
            margin = 118  # 1cm à 300 DPI
            usable_width = a4_width - (2 * margin)
            usable_height = a4_height - (2 * margin)

            # Calculer combien d'images peuvent tenir par page
            barcode_width, barcode_height = 413, 177  # 3.5cm x 1.5cm
            qr_width, qr_height = 236, 236  # 2cm x 2cm

            # Espacement entre les images
            spacing = 30  # 0.25cm à 300 DPI

            # Calculer le nombre de colonnes et lignes pour les codes-barres
            barcode_cols = max(1, (usable_width + spacing) // (barcode_width + spacing))
            barcode_rows = max(1, (usable_height + spacing) // (barcode_height + spacing))
            barcodes_per_page = barcode_cols * barcode_rows

            # Calculer le nombre de colonnes et lignes pour les QR codes
            qr_cols = max(1, (usable_width + spacing) // (qr_width + spacing))
            qr_rows = max(1, (usable_height + spacing) // (qr_height + spacing))
            qr_per_page = qr_cols * qr_rows

            print(f"Disposition A4: {barcode_cols}x{barcode_rows} codes-barres, {qr_cols}x{qr_rows} QR codes par page")

            temp_folder = tempfile.mkdtemp()
            pdf_files = []

            # Créer les pages pour les codes-barres
            if barcode_images:
                barcode_pages = math.ceil(len(barcode_images) / barcodes_per_page)
                for page in range(barcode_pages):
                    page_img = Image.new('RGB', (a4_width, a4_height), 'white')

                    start_idx = page * barcodes_per_page
                    end_idx = min(start_idx + barcodes_per_page, len(barcode_images))

                    for i, barcode in enumerate(barcode_images[start_idx:end_idx]):
                        row = i // barcode_cols
                        col = i % barcode_cols

                        x = margin + col * (barcode_width + spacing)
                        y = margin + row * (barcode_height + spacing)

                        page_img.paste(barcode, (x, y))

                    pdf_path = os.path.join(temp_folder, f"codes_barres_page_{page+1}.pdf")
                    page_img.save(pdf_path, 'PDF', resolution=300.0)
                    pdf_files.append(pdf_path)

            # Créer les pages pour les QR codes
            if qr_images:
                qr_pages = math.ceil(len(qr_images) / qr_per_page)
                for page in range(qr_pages):
                    page_img = Image.new('RGB', (a4_width, a4_height), 'white')

                    start_idx = page * qr_per_page
                    end_idx = min(start_idx + qr_per_page, len(qr_images))

                    for i, qr_code in enumerate(qr_images[start_idx:end_idx]):
                        row = i // qr_cols
                        col = i % qr_cols

                        x = margin + col * (qr_width + spacing)
                        y = margin + row * (qr_height + spacing)

                        page_img.paste(qr_code, (x, y))

                    pdf_path = os.path.join(temp_folder, f"qr_codes_page_{page+1}.pdf")
                    page_img.save(pdf_path, 'PDF', resolution=300.0)
                    pdf_files.append(pdf_path)

            # Ouvrir les fichiers PDF pour impression
            if pdf_files:
                for pdf_file in pdf_files:
                    os.startfile(pdf_file)

                return {
                    "success": True,
                    "message": f"Fichiers PDF créés pour impression !\n{len(pdf_files)} pages générées\nOuverts pour impression sur {printer_name}",
                    "files_created": len(pdf_files),
                    "temp_folder": temp_folder,
                    "barcode_pages": len([f for f in pdf_files if "codes_barres" in f]),
                    "qr_pages": len([f for f in pdf_files if "qr_codes" in f])
                }
            else:
                return {"success": False, "message": "Aucun fichier PDF créé"}

        except Exception as e:
            print(f"Erreur création disposition: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur création disposition: {str(e)}"}

    def generate_framed_images(self):
        """Générer des images encadrées 6cm x 3cm avec logo Renault"""
        try:
            if not generator.input_file or not generator.output_folder:
                return {"success": False, "message": "Veuillez sélectionner un fichier et un dossier de destination"}

            if not os.path.exists(generator.input_file):
                return {"success": False, "message": f"Fichier non trouvé: {generator.input_file}"}

            if not os.path.exists(generator.output_folder):
                return {"success": False, "message": f"Dossier non trouvé: {generator.output_folder}"}

            # Import différé pandas
            pandas_module = get_pandas()
            if not pandas_module:
                return {"success": False, "message": "Erreur chargement pandas"}

            # Lire le fichier Excel avec gestion des ressources
            try:
                with open(generator.input_file, 'rb') as file:
                    df = pandas_module.read_excel(file)
                # Forcer la libération des ressources
                import gc
                gc.collect()
            except Exception as e:
                return {"success": False, "message": f"Erreur lecture fichier Excel: {str(e)}"}

            if df.empty:
                return {"success": False, "message": "Le fichier Excel est vide"}

            # Convertir en données
            data = df.values.tolist()

            # Créer le dossier pour les images encadrées
            base_name = os.path.splitext(os.path.basename(generator.input_file))[0]
            images_folder = os.path.join(generator.output_folder, f"{base_name}_Images_Encadrees")
            os.makedirs(images_folder, exist_ok=True)

            # Générer les images encadrées
            generated_count = 0
            total_rows = len(data)

            for row_index, row_data in enumerate(data):
                if generator.is_cancelled:
                    return {"success": False, "message": "Opération annulée par l'utilisateur"}

                try:
                    # Générer l'image encadrée PIL directement
                    framed_pil_img = generator.generate_framed_pil_image(row_data, row_index + 1)

                    if framed_pil_img:
                        # Sauvegarder l'image
                        filename = f"Image_Encadree_{row_index + 1:03d}.png"
                        filepath = os.path.join(images_folder, filename)

                        # Sauvegarder l'image PIL avec haute résolution
                        framed_pil_img.save(filepath, 'PNG', dpi=(300, 300))

                        generated_count += 1

                    # Mise à jour progression
                    progress = int((row_index + 1) / total_rows * 100)
                    generator.update_progress_callback(progress)

                except Exception as e:
                    print(f"Erreur génération image ligne {row_index + 1}: {e}")
                    continue

            return {
                "success": True,
                "message": f"Images encadrées générées avec succès !\n{generated_count} images créées dans:\n{images_folder}",
                "generated_count": generated_count,
                "folder": images_folder
            }

        except Exception as e:
            print(f"Erreur génération images encadrées: {e}")
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def generate_codes_with_user_choice(self, generate_barcodes=True, generate_qr=True, file_choice=None, original_filename=None):
        """Générer les codes avec le choix utilisateur pour les conflits"""
        try:
            if not generator.input_file or not generator.output_folder:
                return {"success": False, "message": "Veuillez sélectionner un fichier et un dossier de destination"}

            # Si on a un choix utilisateur, traiter le conflit
            if file_choice and original_filename:
                base_name = os.path.splitext(original_filename)[0]

                if file_choice == 'cancel':
                    return {"success": False, "message": "Génération annulée par l'utilisateur"}
                elif file_choice == 'rename':
                    # Générer un nouveau nom avec timestamp
                    import datetime
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_filename = f"{base_name}_{timestamp}.xlsx"
                elif file_choice == 'overwrite':
                    # Garder le nom original
                    output_filename = original_filename
                else:
                    return {"success": False, "message": "Choix invalide"}

                output_path = os.path.join(generator.output_folder, output_filename)

                # Continuer la génération avec le nom choisi
                return self._continue_generation(generate_barcodes, generate_qr, output_path)
            else:
                # Génération normale
                return self.generate_codes(generate_barcodes, generate_qr)

        except Exception as e:
            print(f"Erreur génération avec choix: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def _continue_generation(self, generate_barcodes, generate_qr, output_path):
        """Continuer la génération avec un chemin de fichier spécifique"""
        try:
            # Import différé pandas
            pandas_module = get_pandas()
            if not pandas_module:
                return {"success": False, "message": "Erreur chargement pandas"}

            # Lire le fichier Excel avec gestion des ressources
            with open(generator.input_file, 'rb') as file:
                df = pandas_module.read_excel(file)
            print(f"Fichier lu: {len(df)} lignes, {len(df.columns)} colonnes")

            # Forcer la libération des ressources
            import gc
            gc.collect()

            if df.empty:
                return {"success": False, "message": "Le fichier Excel est vide"}

            # Sauvegarder les en-têtes originaux
            original_headers = df.columns.tolist()
            print(f"En-têtes: {original_headers}")

            # Convertir en données
            data = df.values.tolist()

            # Import différé openpyxl
            openpyxl_module = get_openpyxl()
            if not openpyxl_module:
                return {"success": False, "message": "Erreur chargement openpyxl"}

            # Créer le nouveau workbook
            workbook = openpyxl_module['Workbook']()
            worksheet = workbook.active
            worksheet.title = "Données avec Codes"

            # Styles (même code que dans generate_codes)
            header_font = openpyxl_module['Font'](name='Segoe UI', size=11, bold=True, color='FFFFFF')
            header_fill = openpyxl_module['PatternFill'](start_color='003366', end_color='003366', fill_type='solid')
            header_alignment = openpyxl_module['Alignment'](horizontal='center', vertical='center')

            # Bordures
            thin_border = openpyxl_module['Side'](border_style="thin", color="CCCCCC")
            header_border = openpyxl_module['Border'](left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)

            # Couleur alternée pour les lignes
            fill_color = openpyxl_module['PatternFill'](start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')

            # Préparer les en-têtes
            headers = original_headers.copy()
            barcode_col = None
            qr_col = None

            if generate_barcodes:
                headers.append("Code-Barres")
                barcode_col = len(headers)

            if generate_qr:
                headers.append("QR Code")
                qr_col = len(headers)

            # Écrire les en-têtes
            for col_num, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col_num, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = header_border

            # Traitement des données
            total_rows = len(data)
            for row_index, row_data in enumerate(data):
                if generator.is_cancelled:
                    return {"success": False, "message": "Opération annulée"}

                row_num = row_index + 2

                # Écrire les données originales
                for col_index, value in enumerate(row_data):
                    cell = worksheet.cell(row=row_num, column=col_index + 1, value=value)
                    cell.border = openpyxl_module['Border'](left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)

                    if row_index % 2 == 0:
                        cell.fill = fill_color

                # Générer les codes
                col1 = row_data[0] if len(row_data) > 0 else ""
                col2 = row_data[1] if len(row_data) > 1 else ""
                barcode_data = f"{col1}-{col2}"

                # QR Code : TOUTES les données de la ligne (format compatible iOS)
                qr_data_parts = []
                for i, value in enumerate(row_data):
                    if i < len(original_headers) and value is not None:
                        # Format simple compatible : Nom=Valeur
                        qr_data_parts.append(f"{original_headers[i]}={value}")
                qr_data = "\n".join(qr_data_parts)  # Format multi-lignes pour iOS

                # Code-barre (seulement si demandé)
                if generate_barcodes and barcode_col:
                    barcode_cell = worksheet.cell(row=row_num, column=barcode_col, value="")
                    barcode_cell.border = openpyxl_module['Border'](left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)

                    if row_index % 2 == 0:
                        barcode_cell.fill = fill_color

                    # Générer l'image du code-barre
                    barcode_img = generator.generate_barcode_image(barcode_data)
                    if barcode_img:
                        barcode_img.anchor = f"{chr(64 + barcode_col)}{row_num}"
                        worksheet.add_image(barcode_img)

                # QR Code (seulement si demandé)
                if generate_qr and qr_col:
                    qr_cell = worksheet.cell(row=row_num, column=qr_col, value="")
                    qr_cell.border = openpyxl_module['Border'](left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)

                    if row_index % 2 == 0:
                        qr_cell.fill = fill_color

                    # Générer l'image du QR code
                    qr_img = generator.generate_qr_image(qr_data)
                    if qr_img:
                        qr_img.anchor = f"{chr(64 + qr_col)}{row_num}"
                        worksheet.add_image(qr_img)

                # Mise à jour progression
                progress = ((row_index + 1) / total_rows) * 100
                generator.update_progress_callback(int(progress))

            # Ajuster les largeurs des colonnes
            for col_num in range(1, len(headers) + 1):
                if col_num == barcode_col:
                    worksheet.column_dimensions[chr(64 + col_num)].width = 15
                elif col_num == qr_col:
                    worksheet.column_dimensions[chr(64 + col_num)].width = 10
                else:
                    worksheet.column_dimensions[chr(64 + col_num)].width = 20

            # Ajuster les hauteurs des lignes pour les images
            for row_num in range(2, total_rows + 2):
                worksheet.row_dimensions[row_num].height = 50

            # Sauvegarder le fichier
            workbook.save(output_path)
            workbook.close()

            # Stocker le fichier généré pour l'export
            generator.last_generated_file = output_path

            print(f"Fichier généré: {output_path}")
            return {
                "success": True,
                "message": f"Fichier généré avec succès !\nEmplacement: {output_path}",
                "file_path": output_path,
                "rows_processed": total_rows
            }

        except Exception as e:
            print(f"Erreur lors de la génération continue: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def generate_framed_images(self):
        """PRENDRE les codes du fichier Excel et créer étiquettes photo"""
        try:
            # Variables pour les types de codes (pour compatibilité)
            include_barcodes = True
            include_qr = True

            print(f"CREATION ETIQUETTES - Codes-barres: {include_barcodes}, QR: {include_qr}")

            if not include_barcodes and not include_qr:
                return {"success": False, "message": "Selectionnez au moins un type de code"}

            # Import modules
            openpyxl_module = get_openpyxl()
            PIL_module = get_PIL()
            if not openpyxl_module or not PIL_module:
                return {"success": False, "message": "Erreur chargement modules"}

            # Ouvrir le fichier Excel avec les codes
            print(f"Ouverture fichier: {generator.last_generated_file}")
            workbook = openpyxl_module['load_workbook'](generator.last_generated_file)
            worksheet = workbook.active

            # Créer dossier de sortie
            base_name = os.path.splitext(os.path.basename(generator.last_generated_file))[0]
            etiquettes_folder = os.path.join(generator.output_folder, f"{base_name}_Etiquettes_Renault")
            os.makedirs(etiquettes_folder, exist_ok=True)
            print(f"Dossier cree: {etiquettes_folder}")

            # EXTRAIRE LES IMAGES DU FICHIER EXCEL
            extracted_codes = []
            print(f"Extraction de {len(worksheet._images)} images...")

            for i, image in enumerate(worksheet._images):
                try:
                    # Récupérer l'image
                    image_data = image._data()
                    pil_img = PIL_module.open(io.BytesIO(image_data))
                    width, height = pil_img.size

                    # Déterminer le type par dimensions
                    if width > height:  # Code-barres
                        code_type = "barcode"
                        if not include_barcodes:
                            continue
                    else:  # QR code
                        code_type = "qr"
                        if not include_qr:
                            continue

                    extracted_codes.append({
                        'image': pil_img,
                        'type': code_type,
                        'index': i + 1
                    })
                    print(f"OK Code {code_type} #{i+1} extrait ({width}x{height})")

                except Exception as e:
                    print(f"ERREUR extraction image {i+1}: {e}")

            # Fermer le workbook et forcer la libération des ressources
            workbook.close()
            import gc
            gc.collect()

            if not extracted_codes:
                return {"success": False, "message": "Aucun code trouve correspondant a votre selection"}

            # CRÉER LES ÉTIQUETTES PHOTO
            success_count = 0
            total_codes = len(extracted_codes)
            print(f"Creation de {total_codes} etiquettes...")

            for i, code_data in enumerate(extracted_codes):
                try:
                    # Créer l'étiquette photo Renault
                    etiquette_img = self._create_renault_photo_etiquette(
                        code_data['image'],
                        code_data['type'],
                        code_data['index'],
                        PIL_module
                    )

                    if etiquette_img:
                        # Sauvegarder
                        filename = f"Etiquette_{code_data['type'].upper()}_{code_data['index']:03d}.png"
                        filepath = os.path.join(etiquettes_folder, filename)
                        etiquette_img.save(filepath, 'PNG', dpi=(300, 300))
                        success_count += 1
                        print(f"Etiquette creee: {filename}")

                    # Progression
                    progress = int((i + 1) / total_codes * 100)
                    generator.update_progress_callback(progress)

                except Exception as e:
                    print(f"Erreur etiquette {i+1}: {e}")

            return {
                "success": True,
                "message": f"{success_count} etiquettes Renault creees !\nDossier: {etiquettes_folder}\nFormat: PNG 300 DPI (7cm x 4cm)",
                "folder": etiquettes_folder,
                "success_count": success_count
            }

        except Exception as e:
            print(f"ERREUR: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def _create_renault_photo_etiquette(self, code_img, code_type, index, PIL_module):
        """Créer une étiquette Renault format photo avec logo et code"""
        try:
            # Dimensions étiquette VERTICALE : 3.9cm x 7cm à 300 DPI
            etiquette_width = int(3.9 * 300 / 2.54)   # 461 pixels (largeur)
            etiquette_height = int(7 * 300 / 2.54)     # 827 pixels (hauteur)

            # Créer l'image de base (fond blanc)
            etiquette = PIL_module.new('RGB', (etiquette_width, etiquette_height), color='white')

            # Ajouter cadre orange Renault (bordure fine)
            from PIL import ImageDraw
            draw = ImageDraw.Draw(etiquette)
            renault_orange = '#FF6600'  # Couleur orange Renault
            border_width = 3  # Bordure fine

            # Dessiner le cadre orange autour de l'étiquette
            for i in range(border_width):
                draw.rectangle([i, i, etiquette_width-1-i, etiquette_height-1-i],
                             outline=renault_orange, width=1)

            # Remplacer le logo par un nom de grande taille + description
            # Taille uniforme pour tous les tickets (même que la première fonction)
            nom_font_size = 80  # Taille agrandie pour le nom principal

            # Position à 0,8cm du haut du ticket
            position_haut_cm = 0.8
            position_haut_pixels = int(position_haut_cm * 300 / 2.54)  # Convertir 0,8cm en pixels

            # Dessiner le nom de grande taille centré en haut
            try:
                from PIL import ImageFont
                # Utiliser une police de grande taille pour remplacer le logo
                try:
                    # Essayer d'utiliser une police en gras pour le nom principal
                    nom_font = ImageFont.truetype("arialbd.ttf", nom_font_size)
                    print(f"Police nom utilisée: Arial Bold {nom_font_size}pt")
                except:
                    try:
                        nom_font = ImageFont.truetype("arial.ttf", nom_font_size)
                        print(f"Police nom utilisée: Arial {nom_font_size}pt")
                    except:
                        nom_font = ImageFont.load_default()
                        print("Police nom utilisée: Police par défaut")

                # Texte du nom à afficher
                nom_text = "SOMACA"

                # Calculer la position du nom (centré horizontalement)
                nom_bbox = draw.textbbox((0, 0), nom_text, font=nom_font)
                nom_text_width = nom_bbox[2] - nom_bbox[0]
                nom_text_height = nom_bbox[3] - nom_bbox[1]
                nom_x = (etiquette_width - nom_text_width) // 2

                # Position à 0,8cm du haut du ticket
                nom_y = position_haut_pixels

                # Couleur du nom (noir sur fond blanc)
                nom_color = 'black'

                # Dessiner le nom
                draw.text((nom_x, nom_y), nom_text, fill=nom_color, font=nom_font)
                print(f"Nom '{nom_text}' ajouté à position ({nom_x}, {nom_y}) couleur {nom_color}")


                nom_present = True

            except Exception as e:
                print(f"Erreur ajout nom: {e}")
                nom_present = False
                nom_text_height = 60  # Valeur par défaut pour la hauteur du nom

            # Créer zone blanche pour le code
            if code_type == "barcode":
                # Zone blanche pour code-barres : 4.5cm x 2.5cm
                code_zone_width = int(4.5 * 300 / 2.54)  # 531 pixels
                code_zone_height = int(2.5 * 300 / 2.54)  # 295 pixels
            else:  # QR code
                # Zone blanche pour QR code : 3.0cm x 3.0cm
                code_zone_width = int(3.0 * 300 / 2.54)  # 354 pixels
                code_zone_height = int(3.0 * 300 / 2.54)  # 354 pixels

            # Créer la zone blanche
            code_zone = PIL_module.new('RGB', (code_zone_width, code_zone_height), color='white')

            # Redimensionner le code pour qu'il rentre dans la zone blanche avec marge
            margin = 20
            max_code_width = code_zone_width - (margin * 2)
            max_code_height = code_zone_height - (margin * 2)

            # Calculer les nouvelles dimensions en gardant le ratio
            code_ratio = code_img.width / code_img.height
            if code_ratio > 1:  # Plus large que haut
                new_width = min(max_code_width, code_img.width)
                new_height = int(new_width / code_ratio)
            else:  # Plus haut que large ou carré
                new_height = min(max_code_height, code_img.height)
                new_width = int(new_height * code_ratio)

            # Redimensionner le code
            resized_code = code_img.resize((new_width, new_height), PIL_module.Resampling.LANCZOS if hasattr(PIL_module, 'Resampling') else PIL_module.LANCZOS)

            # Centrer le code dans la zone blanche
            code_x = (code_zone_width - new_width) // 2
            code_y = (code_zone_height - new_height) // 2
            code_zone.paste(resized_code, (code_x, code_y))

            # Positionner la zone blanche sur l'étiquette
            zone_x = (etiquette_width - code_zone_width) // 2
            if nom_present:
                # Position sous le nom avec espace de 0,8cm
                espace_nom_code_cm = 0.8
                espace_nom_code_pixels = int(espace_nom_code_cm * 300 / 2.54)  # Convertir 0,8cm en pixels
                zone_y = position_haut_pixels + nom_text_height + espace_nom_code_pixels
            else:
                zone_y = 80  # Position par défaut si pas de nom
            etiquette.paste(code_zone, (zone_x, zone_y))

            # Ajouter le numéro en bas (texte blanc)
            try:
                from PIL import ImageDraw, ImageFont
                draw = ImageDraw.Draw(etiquette)

                # Texte du numéro
                text = f"{code_type.upper()} #{index:03d}"

                # Essayer de charger une police, sinon utiliser la police par défaut
                try:
                    font = ImageFont.truetype("arial.ttf", 24)
                except:
                    font = ImageFont.load_default()

                # Calculer la position du texte (centré en bas)
                text_bbox = draw.textbbox((0, 0), text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_x = (etiquette_width - text_width) // 2
                text_y = etiquette_height - 50

                # Dessiner le texte en blanc
                draw.text((text_x, text_y), text, fill='white', font=font)

            except Exception as text_error:
                print(f"Erreur ajout texte: {text_error}")

            return etiquette

        except Exception as e:
            print(f"Erreur création étiquette photo: {e}")
            return None

    def open_folder_explorer(self, folder_path):
        """Ouvrir un dossier dans l'explorateur Windows"""
        try:
            import subprocess
            import platform

            if platform.system() == "Windows":
                subprocess.run(["explorer", folder_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])

            return {"success": True}
        except Exception as e:
            print(f"Erreur ouverture dossier: {e}")
            return {"success": False, "message": str(e)}

    def generate_renault_etiquettes(self):
        """PRENDRE les codes du fichier Excel et demander le choix utilisateur"""
        return generator.generate_renault_etiquettes()

    def generate_renault_etiquettes_with_choice(self, include_barcodes=True, include_qr=True, background_color="black"):
        """PRENDRE les codes du fichier Excel et créer étiquettes photo"""
        return generator.generate_renault_etiquettes_with_choice(include_barcodes, include_qr, background_color)

    def generate_renault_tickets_with_colors(self, selected_colors):
        """Générer les tickets Renault dans les couleurs sélectionnées"""
        return generator.generate_renault_tickets_with_colors(selected_colors)

def main():
    """Fonction principale"""
    # Chemin vers le dossier web
    web_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'web')
    
    # Créer l'API
    api = Api()

    # Créer la fenêtre native
    window = webview.create_window(
        'Générateur QR & Code à Barre',
        url=os.path.join(web_dir, 'index.html'),
        js_api=api,
        width=1200,
        height=800,
        resizable=True
    )
    
    # Stocker la référence de la fenêtre
    generator.window = window
    
    # Assigner la fenêtre au générateur pour les mises à jour en temps réel
    generator.window = window

    # Démarrer l'application
    webview.start(debug=False)

if __name__ == '__main__':
    main()
