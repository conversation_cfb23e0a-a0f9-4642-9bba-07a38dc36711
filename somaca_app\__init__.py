#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOMACA - Générateur de Codes-Barres et QR Codes
Application native Windows avec interface HTML

Développé par: Imad Elberrouagui
Version: 3.0 - Architecture Modulaire Refactorisée

Ce package contient tous les modules nécessaires pour la génération
de codes-barres et QR codes avec interface native.
"""

__version__ = "3.0.0"
__author__ = "Imad Elberrouagui"
__email__ = "<EMAIL>"
__description__ = "Générateur de Codes-Barres et QR Codes - SOMACA"

# Imports principaux pour faciliter l'utilisation
from .core.barcode_generator import BarcodeGenerator
from .core.qr_generator import QRGenerator
from .core.excel_processor import ExcelProcessor
from .core.renault_generator import RenaultGenerator

__all__ = [
    'BarcodeGenerator',
    'QRGenerator', 
    'ExcelProcessor',
    'RenaultGenerator'
]
