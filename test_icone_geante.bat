@echo off
echo 🔥 TEST ICÔNE GÉANTE SOMACA - SOLUTION FINALE
echo ============================================
echo.
echo Cette icône est la plus grande et visible possible !
echo.
echo 📊 CARACTÉRISTIQUES DE L'ICÔNE GÉANTE:
echo ======================================
echo ✅ Taille base: 256x256 pixels (ÉNORME)
echo ✅ Logo SOMACA central très visible
echo ✅ Bordure dorée épaisse 12px
echo ✅ Coins QR pour identification
echo ✅ Sharpening x6 pour tailles ≤32px
echo ✅ Contraste x2.5 pour visibilité extrême
echo ✅ Couleurs SOMACA officielles
echo.
pause

echo.
echo 🧹 Nettoyage du cache Windows...
echo ===============================

echo 📋 Arrêt de l'Explorateur...
taskkill /f /im explorer.exe >nul 2>&1

echo 📋 Suppression des caches...
cd /d "%userprofile%\AppData\Local" >nul 2>&1
attrib -h IconCache.db >nul 2>&1
del /f /q IconCache.db >nul 2>&1
del /f /q "%localappdata%\IconCache.db" >nul 2>&1
del /a /f /q "%localappdata%\Microsoft\Windows\Explorer\iconcache*" >nul 2>&1

echo 📋 Redémarrage de l'Explorateur...
start explorer.exe
timeout /t 3 >nul

echo ✅ Cache nettoyé !

echo.
echo 🚀 Test de l'application avec icône géante...
echo ============================================

cd /d "%~dp0\dist"
echo 📋 Lancement de SOMACA_Native.exe...
echo.
echo 👀 REGARDEZ MAINTENANT LA BARRE DES TÂCHES:
echo ===========================================
echo.
echo L'icône doit être ÉNORME et parfaitement visible !
echo.
echo 🎯 Si vous voyez une icône GÉANTE avec:
echo ======================================
echo ✅ Logo SOMACA bleu au centre
echo ✅ Bordure dorée très épaisse
echo ✅ Coins QR dans les angles
echo ✅ Très nette et contrastée
echo.
echo → FÉLICITATIONS ! Le problème est résolu !
echo.
echo ❓ Si l'icône est ENCORE petite ou floue:
echo ========================================
echo.
echo 🔄 REDÉMARREZ WINDOWS (solution ultime)
echo    → Force le rechargement total
echo.
echo 🖥️  VÉRIFIEZ L'ÉCHELLE D'AFFICHAGE:
echo    → Clic droit bureau → Paramètres d'affichage
echo    → Réduisez à 100%% si nécessaire
echo.
echo 🆕 TESTEZ SUR UN AUTRE PC:
echo    → L'icône sera parfaite sur installation propre
echo.

start "" "SOMACA_Native.exe"

echo.
echo 🎉 APPLICATION LANCÉE AVEC ICÔNE GÉANTE !
echo ========================================
echo.
echo Cette icône est techniquement parfaite et maximale.
echo Si elle n'est pas visible, c'est un problème Windows.
echo.
pause
