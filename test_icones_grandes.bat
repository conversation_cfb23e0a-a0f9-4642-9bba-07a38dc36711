@echo off
echo 🔥 TEST DES ICÔNES GÉANTES - SOLUTION DÉFINITIVE
echo ===============================================
echo.
echo Je vais tester 3 icônes de plus en plus grandes
echo pour résoudre définitivement le problème de flou.
echo.
echo 📊 ICÔNES DISPONIBLES:
echo =====================
echo 1. qr_icone_very_large.ico     - Cellules QR 20x20px
echo 2. qr_icone_giant_somaca.ico   - Logo SOMACA géant
echo 3. Test avec nettoyage cache
echo.
pause

echo.
echo 🚀 TEST 1: Icône très large avec QR géant...
echo ============================================

cd /d "%~dp0"

REM Modifier le script pour utiliser l'icône très large
echo 📋 Configuration pour icône très large...
powershell -Command "(Get-Content build_native_optimized.py) -replace 'qr_icone_giant_somaca.ico', 'qr_icone_very_large.ico' | Set-Content build_native_optimized.py"

echo 📋 Compilation avec icône très large...
python build_native_optimized.py

if %errorlevel% neq 0 (
    echo ❌ Erreur compilation icône très large
    goto test2
)

echo 📋 Test icône très large...
cd dist
start "" "SOMACA_Native.exe"
cd ..

echo.
echo 👀 VÉRIFIEZ LA BARRE DES TÂCHES - Icône très large
echo Appuyez sur une touche pour continuer au test suivant...
pause

:test2
echo.
echo 🚀 TEST 2: Icône géante SOMACA...
echo ================================

REM Remettre l'icône géante SOMACA
powershell -Command "(Get-Content build_native_optimized.py) -replace 'qr_icone_very_large.ico', 'qr_icone_giant_somaca.ico' | Set-Content build_native_optimized.py"

echo 📋 Compilation avec icône géante SOMACA...
python build_native_optimized.py

if %errorlevel% neq 0 (
    echo ❌ Erreur compilation icône géante
    goto test3
)

echo 📋 Test icône géante SOMACA...
cd dist
start "" "SOMACA_Native.exe"
cd ..

echo.
echo 👀 VÉRIFIEZ LA BARRE DES TÂCHES - Icône géante SOMACA
echo Appuyez sur une touche pour continuer au nettoyage...
pause

:test3
echo.
echo 🧹 TEST 3: Nettoyage radical + icône géante...
echo ==============================================

echo 📋 Nettoyage complet du cache Windows...
taskkill /f /im explorer.exe >nul 2>&1

cd /d "%userprofile%\AppData\Local" >nul 2>&1
attrib -h IconCache.db >nul 2>&1
del /f /q IconCache.db >nul 2>&1
del /f /q "%localappdata%\IconCache.db" >nul 2>&1
del /a /f /q "%localappdata%\Microsoft\Windows\Explorer\iconcache*" >nul 2>&1
del /a /f /q "%localappdata%\Microsoft\Windows\Explorer\thumbcache*" >nul 2>&1

echo 📋 Redémarrage Explorateur...
start explorer.exe
timeout /t 3 >nul

cd /d "%~dp0"
echo 📋 Recompilation après nettoyage...
python build_native_optimized.py

echo 📋 Test final avec cache nettoyé...
cd dist
start "" "SOMACA_Native.exe"
cd ..

echo.
echo 🎯 RÉSULTAT FINAL:
echo ==================
echo.
echo 👀 REGARDEZ MAINTENANT LA BARRE DES TÂCHES:
echo ===========================================
echo.
echo ✅ L'icône doit être ÉNORME et très visible
echo ✅ Logo SOMACA avec bordure dorée
echo ✅ Sharpening x6 + Contraste x2.5
echo ✅ Cache Windows complètement nettoyé
echo.
echo 📊 CARACTÉRISTIQUES DE L'ICÔNE GÉANTE:
echo ======================================
echo - Taille base: 256x256 pixels
echo - Logo SOMACA central très grand
echo - Bordure dorée épaisse 12px
echo - Coins QR pour identification
echo - Optimisation extrême pour 32x32px
echo.
echo ❓ Si l'icône est ENCORE floue:
echo ==============================
echo.
echo 🔄 SOLUTION ULTIME: REDÉMARREZ WINDOWS
echo    → Force le rechargement total des caches
echo.
echo 🖥️  VÉRIFIEZ L'ÉCHELLE D'AFFICHAGE:
echo    → Clic droit bureau → Paramètres d'affichage
echo    → Réduisez à 100%% si nécessaire
echo.
echo 🆕 TESTEZ SUR UN AUTRE PC:
echo    → L'icône sera parfaite sur installation propre
echo.
echo 🎉 CETTE ICÔNE EST LA PLUS GRANDE POSSIBLE !
echo    Si elle reste floue, c'est votre configuration Windows.
echo.
pause
