#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Progress Manager - SOMACA

Gestionnaire de progression pour les opérations longues.
Fournit un système de callbacks et de verrouillage pour éviter les conflits.
"""

import threading
import logging
from typing import Optional, Callable, Any
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ProgressManager:
    """
    Gestionnaire de progression pour les opérations longues.
    
    Utilise un système de verrouillage pour éviter les conflits
    entre plusieurs opérations simultanées.
    """
    
    def __init__(self):
        self.progress = 0
        self.is_cancelled = False
        self.current_thread: Optional[threading.Thread] = None
        self.window = None  # Référence vers la fenêtre WebView
        
        # Système de verrouillage
        self.progress_locked = False
        self.progress_owner: Optional[str] = None
        self._lock = threading.Lock()
        
        # Callbacks
        self.progress_callback: Optional[Callable[[int], None]] = None
        self.completion_callback: Optional[Callable[[dict], None]] = None
        
        logger.debug("Gestionnaire de progression initialisé")
    
    def set_window(self, window: Any):
        """
        Définir la référence vers la fenêtre WebView.
        
        Args:
            window: Instance de la fenêtre WebView
        """
        self.window = window
        logger.debug("Référence fenêtre WebView définie")
    
    def set_progress_callback(self, callback: Callable[[int], None]):
        """
        Définir le callback de progression.
        
        Args:
            callback: Fonction appelée avec le pourcentage de progression
        """
        self.progress_callback = callback
        logger.debug("Callback de progression défini")
    
    def set_completion_callback(self, callback: Callable[[dict], None]):
        """
        Définir le callback de fin d'opération.
        
        Args:
            callback: Fonction appelée avec le résultat final
        """
        self.completion_callback = callback
        logger.debug("Callback de fin défini")
    
    def update_progress(self, percentage: int, owner: str = None):
        """
        Mettre à jour la progression.
        
        Args:
            percentage: Pourcentage de progression (0-100)
            owner: Propriétaire de la mise à jour
        """
        with self._lock:
            # Vérifier le verrouillage
            if self.progress_locked and self.progress_owner != owner:
                logger.debug(f"Progression verrouillée par {self.progress_owner}, {owner} ignoré ({percentage}%)")
                return
            
            # Protection contre la régression
            if percentage < self.progress and self.progress >= 99:
                logger.debug(f"Protection progression: {percentage}% ignoré (actuel: {self.progress}%)")
                return
            
            # Valider le pourcentage
            percentage = max(0, min(100, percentage))
            
            self.progress = percentage
            logger.debug(f"Progression mise à jour: {percentage}% (par {owner or 'système'})")
            
            # Appeler le callback si défini
            if self.progress_callback:
                try:
                    self.progress_callback(percentage)
                except Exception as e:
                    logger.error(f"Erreur callback progression: {e}")
            
            # Mettre à jour l'interface WebView si disponible
            if self.window:
                try:
                    self.window.evaluate_js(f"update_progress({percentage})")
                except Exception as e:
                    logger.error(f"Erreur mise à jour WebView: {e}")
    
    def lock_progress(self, owner: str):
        """
        Verrouiller la progression pour un propriétaire.
        
        Args:
            owner: Nom du propriétaire
        """
        with self._lock:
            self.progress_locked = True
            self.progress_owner = owner
            logger.info(f"Progression verrouillée par: {owner}")
    
    def unlock_progress(self):
        """Déverrouiller la progression"""
        with self._lock:
            previous_owner = self.progress_owner
            self.progress_locked = False
            self.progress_owner = None
            logger.info(f"Progression déverrouillée (était: {previous_owner})")
    
    def cancel_operation(self):
        """Annuler l'opération en cours"""
        with self._lock:
            logger.info("Demande d'annulation reçue")
            self.is_cancelled = True
            
            # Si un thread est en cours, le marquer pour arrêt
            if self.current_thread and self.current_thread.is_alive():
                logger.info("Thread en cours marqué pour arrêt")
                # Le thread doit vérifier self.is_cancelled périodiquement
    
    def reset(self):
        """Réinitialiser le gestionnaire pour une nouvelle opération"""
        with self._lock:
            self.progress = 0
            self.is_cancelled = False
            self.current_thread = None
            self.progress_locked = False
            self.progress_owner = None
            logger.debug("Gestionnaire de progression réinitialisé")
    
    def start_operation(self, operation_name: str = "Opération"):
        """
        Démarrer une nouvelle opération.
        
        Args:
            operation_name: Nom de l'opération
        """
        self.reset()
        logger.info(f"Démarrage opération: {operation_name}")
    
    def complete_operation(self, result: dict):
        """
        Marquer l'opération comme terminée.
        
        Args:
            result: Résultat de l'opération
        """
        with self._lock:
            if not self.is_cancelled:
                self.progress = 100
                logger.info(f"Opération terminée: {result.get('message', 'Succès')}")
            else:
                logger.info("Opération annulée")
            
            # Appeler le callback de fin si défini
            if self.completion_callback:
                try:
                    self.completion_callback(result)
                except Exception as e:
                    logger.error(f"Erreur callback fin: {e}")
    
    def set_current_thread(self, thread: threading.Thread):
        """
        Définir le thread actuel.
        
        Args:
            thread: Thread de l'opération
        """
        self.current_thread = thread
        logger.debug(f"Thread actuel défini: {thread.name}")
    
    def is_operation_cancelled(self) -> bool:
        """
        Vérifier si l'opération est annulée.
        
        Returns:
            True si annulée, False sinon
        """
        return self.is_cancelled
    
    def get_progress_info(self) -> dict:
        """
        Obtenir les informations de progression.
        
        Returns:
            Dictionnaire avec les informations
        """
        with self._lock:
            return {
                'progress': self.progress,
                'is_cancelled': self.is_cancelled,
                'is_locked': self.progress_locked,
                'owner': self.progress_owner,
                'has_thread': self.current_thread is not None,
                'thread_alive': self.current_thread.is_alive() if self.current_thread else False
            }


# Instance globale du gestionnaire de progression
progress_manager = ProgressManager()
